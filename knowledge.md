# Botmani - Knowledge Base

## Project Overview
- **Name**: <PERSON><PERSON><PERSON>
- **Mission**: Build Your Agentic Ecosystem - Create intelligent agent workflows and ecosystems
- **Description**: A comprehensive agentic platform for building, deploying, and managing AI agents across workflows, ecosystems, and channels
- **Target Audience**: Developers, businesses, and teams who want to build sophisticated AI agent systems
- **Current Version**: 0.1.0
- **Tech Stack**: Next.js, React, TypeScript, Tailwind CSS

## Core Features
- **Agentic Workflows**: Create complex workflows where multiple AI agents collaborate with custom prompts, models, and tools
- **Agent Ecosystems**: Build interconnected agent ecosystems where agents share context and collaborate across domains
- **Multi-Channel Deployment**: Deploy agents across WhatsApp, Telegram, Slack, websites, and custom channels
- **Universal Model Support**: Choose from OpenAI, Anthropic, Gemini, Groq, and Hugging Face models for each agent
- **Rich Tool Integration**: Connect with GitHub, Jira, Gmail, AWS, databases, and 50+ other tools
- **Custom Configuration**: Fine-tune agents with custom system prompts, temperature settings, and model providers
- **Channel Management**: Unified interface for managing agents across multiple communication channels
- **Agent Collaboration**: Agents can work together, share context, and build upon each other's work

## Design System

### Theme
- **Primary Theme**: Pure black dark theme with enhanced glassmorphism aesthetics
- **Design Philosophy**: Ultra-modern, sleek, professional with subtle glass-like transparency effects

### Color Palette
- **Background**: Pure black (#000000) for maximum contrast and modern appearance
- **Glass Effects**: Ultra-subtle semi-transparent whites and grays with backdrop blur
- **Accent Colors**:
  - Primary: Blue/Purple gradients for CTA elements
  - Secondary: Subtle whites and grays with reduced opacity
  - Highlights: Bright accents for important elements

### Typography
- **Primary Font**: Geist Sans (modern, clean)
- **Monospace Font**: Geist Mono (for code/technical elements)
- **Hierarchy**: Clear contrast between headings and body text

### Visual Elements
- **Logo**: logo-light.svg (light version for dark theme)
- **Glass Effects**: backdrop-blur, semi-transparent backgrounds
- **Gradients**: Subtle gradients for depth and visual interest
- **Borders**: Subtle borders with transparency

## Component Standards

### Layout Patterns
- **Full-screen sections**: Hero sections use full viewport height
- **Responsive grid**: Mobile-first approach with breakpoints
- **Glassmorphism cards**: Semi-transparent cards with blur effects
- **Floating elements**: Subtle shadows and elevation

### Interactive Elements
- **Buttons**: Glass-style with hover effects
- **Navigation**: Clean, minimal navigation with collapsible sidebar
- **Cards**: Glassmorphism style with hover animations
- **Forms**: Clean inputs with glass styling
- **SidePanel**: Collapsible navigation with smooth transitions and glassmorphism effects

## Technical Architecture

### File Structure
- **Pages**: `/src/pages/` - Next.js page routing
  - `/src/pages/index.tsx` - Home page
  - `/src/pages/agents.tsx` - Agents dedicated page
  - `/src/pages/workflows.tsx` - Workflows dedicated page
  - `/src/pages/ecosystem.tsx` - Ecosystem dedicated page
  - `/src/pages/auth/` - Authentication pages
  - `/src/pages/_app.tsx` - App wrapper with global providers
- **Components**: `/src/components/` - Reusable UI components
  - `Dashboard.tsx` - Main dashboard with navigation
  - `SidePanel.tsx` - Collapsible side navigation panel with glassmorphism design
  - Individual tab components for different sections
- **Contexts**: `/src/contexts/` - React Context providers for global state management
  - `AppSettingsContext.tsx` - Global app settings management with localStorage persistence
- **Hooks**: `/src/hooks/` - Custom React hooks
- **Types**: `/src/types/` - TypeScript type definitions
- **Styles**: `/src/styles/` - Global styles and Tailwind config
- **Public Assets**: `/public/` - Static assets including logos

### State Management
- **AppSettingsContext**: Global context provider for managing app-wide settings
  - Side panel collapsed/expanded state with persistence
  - localStorage integration for settings persistence across sessions
  - Centralized state management to prevent UI inconsistencies during navigation

### Styling Approach
- **Tailwind CSS**: Primary styling framework
- **CSS Variables**: Custom properties for theme consistency
- **Responsive Design**: Mobile-first responsive patterns

## User Experience Patterns

### Navigation Flow
- **Home Page**: Hero section with clear value proposition and feature showcase
- **Agents Page** (`/agents`): Dedicated page for AI agent creation and management
- **Workflows Page** (`/workflows`): Focused on workflow orchestration and automation
- **Ecosystem Page** (`/ecosystem`): Enterprise-scale interconnected AI systems
- **Dashboard Navigation**: Updated navigation from "Playground" to "Agents"
- **Cross-page Linking**: Consistent navigation between all sections

### Interaction Patterns
- **Hover Effects**: Subtle animations on interactive elements
- **Glass Morphism**: Consistent glass-like styling throughout
- **Smooth Transitions**: Fluid animations between states

## Brand Identity

### Voice & Tone
- **Professional**: Trustworthy and reliable
- **Modern**: Contemporary and innovative
- **Accessible**: Clear and user-friendly

### Visual Identity
- **Glassmorphism**: Primary visual style
- **Dark Theme**: Modern, professional appearance
- **Minimalist**: Clean, uncluttered design

## Development Standards

### Code Style
- **TypeScript**: Type safety throughout
- **React Functional Components**: Modern React patterns
- **Tailwind Classes**: Utility-first styling
- **Responsive Design**: Mobile-first approach

### Performance
- **Next.js Optimization**: Image optimization, code splitting
- **Loading States**: Smooth user experience
- **Accessibility**: WCAG compliance considerations

## Current Implementation Status

### Completed
- [x] Next.js project setup
- [x] Tailwind CSS configuration
- [x] TypeScript configuration
- [x] Logo assets (logo-light.svg)
- [x] Modern glassy dark-themed home page
- [x] Hero section with value proposition
- [x] Feature showcase section
- [x] Responsive design implementation
- [x] Glassmorphism design system
- [x] Custom animations and effects
- [x] Dark theme with gradient accents
- [x] Navigation and footer components
- [x] Stats section with key metrics
- [x] Call-to-action sections
- [x] Featured glassy chat card in hero section
- [x] Visual workflow diagram for Complete Agentic Platform section
- [x] Interactive workflow visualization showing agent collaboration
- [x] Animated data flow between agents, tools, and channels
- [x] Real-time workflow status dashboard
- [x] Floating context cards and responsive design
- [x] **Enhanced Hero Section UI**: Complete redesign with improved visual hierarchy, cleaner animations, and better user experience
- [x] **Modern Typography**: Enhanced title styling with advanced gradient effects and better readability
- [x] **Interactive Feature Pills**: Redesigned feature showcase with hover effects and better information architecture
- [x] **Enhanced CTA Section**: Improved call-to-action buttons with better styling and trust indicators
- [x] **Animated Bot Communication**: Added realistic inter-agent communication with message bubbles, typing indicators, and data flow particles
- [x] **Agent Ecosystem Visualization**: Enhanced agent nodes with brand-colored styling and communication lines
- [x] **Real-time Communication Effects**: Animated message bubbles showing agents collaborating and sharing context in background
- [x] **Clean Background Animation**: Subtle floating orbs and grid patterns combined with dynamic agent interactions
- [x] **Streamlined UI**: Removed distracting chat console to focus on core messaging and agent communication
- [x] **Mobile-First Responsive Design**: Optimized for all screen sizes with progressive enhancement
- [x] **Enhanced Mobile Workflow Section**: Complete responsive redesign of the "Complete Agentic Platform" workflow visualization for mobile devices with improved layout, better spacing, and cleaner element positioning
- [x] **Enhanced CTA Section**: Complete redesign of the call-to-action section with advanced glassmorphism UI, animated background effects, feature highlights, enhanced button design, and comprehensive social proof elements
- [x] **Navigation Update**: Changed "Playground" to "Agents" in dashboard navigation for better clarity
- [x] **Dedicated Pages**: Created separate pages for `/agents`, `/workflows`, and `/ecosystem` with unique content and design
- [x] **Cross-page Navigation**: Implemented consistent navigation structure across all pages
- [x] **Content Differentiation**: Each page focuses on specific aspects of the platform with tailored messaging and features
- [x] **Collapsible SidePanel Component**: Created modular, reusable SidePanel component with glassmorphism design, smooth animations, collapse/expand functionality, and enhanced visual effects
- [x] **SidePanel Scrollability Fix**: Fixed sidebar scrolling issue by removing overflow-hidden constraint and adding proper height definition for scrollable navigation areas
- [x] **Enhanced SidePanel Spacing & Gradient Scrollbar**: Improved sidebar spacing with better padding and margins, created beautiful gradient glassy scrollbar with blue-to-purple gradients, glassmorphism effects, smooth hover animations, and cross-browser compatibility
- [x] **Collapsed Sidebar Optimization**: Fine-tuned collapsed sidebar width to 80px (`w-20`) for optimal space usage while maintaining icon visibility, and implemented hidden scrollbar functionality for collapsed state with cross-browser support
- [x] **Sidebar UI Cleanup**: Removed decorative bot icon element from bottom of expanded sidebar for cleaner, more professional appearance
- [x] **Expanded Sidebar Width Optimization**: Reduced expanded sidebar width from 320px (`w-80`) to 256px (`w-64`) for more compact layout while maintaining usability
- [x] **Professional Sidebar Branding**: Improved sidebar header from generic "Navigation" to branded "Dashboard" with "Agentic Platform" subtitle, featuring gradient text styling that matches the overall theme
- [x] **Enhanced Top Bar Navigation**: Updated navigation tabs with advanced glassmorphism styling, including gradient backgrounds, animated glows, hover particles, and consistent design patterns that match the sidebar aesthetics for seamless user experience across all navigation elements

### In Progress
- [ ] Mobile menu functionality
- [ ] Form components for user interaction

### Planned
- [ ] Agent management dashboard
- [ ] Tool integration interface
- [ ] User authentication
- [ ] Workflow automation features

## Update Log
- **Initial Setup**: Project created with Next.js, React, TypeScript, Tailwind CSS
- **Design System**: Established dark theme with glassmorphism aesthetics
- **Brand Identity**: Defined Botmani as automation-focused platform
- **Theme Enhancement**: Updated to pure black theme (#000000) with enhanced glassmorphism effects and reduced opacity overlays for ultra-modern appearance
- **Navigation & Pages Update**: Changed "Playground" to "Agents" in navigation and created dedicated pages for `/agents`, `/workflows`, and `/ecosystem` with unique content, consistent design patterns, and cross-page navigation structure (January 2025)
- **SidePanel Component Enhancement**: Extracted sidebar functionality into separate SidePanel component with collapsible functionality, enhanced glassmorphism effects, smooth animations, tooltip system for collapsed state, and decorative branding elements (January 2025)
- **Global State Management**: Implemented AppSettingsContext for centralized app settings management with localStorage persistence, ensuring side panel state consistency across page navigation and preventing UI inconsistencies (January 2025)