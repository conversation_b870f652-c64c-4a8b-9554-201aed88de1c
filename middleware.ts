import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Get the botmani-id-token from cookies
  const idToken = request.cookies.get('botmani-id-token')
  
  // List of paths that should be protected (require authentication)
  const protectedPaths = [
    '/agents',
    '/explore', 
    '/models',
    '/settings',
    '/tools',
    '/usage'
  ]
  
  // Check if current path is protected
  const isProtectedPath = protectedPaths.some(path => 
    request.nextUrl.pathname.startsWith(path)
  )
  
  // If accessing a protected path and no id token exists
  if (isProtectedPath && !idToken) {
    // Create response to redirect to home page
    const response = NextResponse.redirect(new URL('/', request.url))
    
    // Clear all authentication cookies
    response.cookies.delete('botmani-session')
    response.cookies.delete('botmani-access-token')
    response.cookies.delete('botmani-google-access-token')
    response.cookies.delete('botmani-id-token')
    response.cookies.delete('botmani-refresh-token')
    
    return response
  }
  
  // For non-protected paths or when id token exists, continue normally
  return NextResponse.next()
}

export const config = {
  // Match all paths except static files and API routes
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}