interface User {
  id: string;
  email: string;
  username: string;
  full_name: string;
  firebase_uid: string | null;
  auth_provider: string;
  is_active: boolean;
  is_superuser: boolean;
  is_verified: boolean;
  bio: string | null;
  avatar_url: string | null;
  created_at: string;
  updated_at: string;
  last_login: string | null;
}

interface UserResponse {
  success: boolean;
  message: string;
  data: {
    user: User;
  };
  meta: {
    timestamp: string;
    version: string;
  };
}

interface ApiError {
  success: false;
  message: string;
  error?: any;
}

// Function to get token from cookies
const getTokenFromCookies = (): string | null => {
  if (typeof document === 'undefined') return null;
  
  const cookies = document.cookie.split(';');
  const tokenCookie = cookies.find(cookie => 
    cookie.trim().startsWith('botmani-id-token=')
  );
  
  if (tokenCookie) {
    return tokenCookie.split('=')[1];
  }
  
  return null;
};

// Get current user information
export const getCurrentUser = async (): Promise<UserResponse | ApiError> => {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    
    if (!baseUrl) {
      return {
        success: false,
        message: 'API base URL is not configured',
        error: 'NEXT_PUBLIC_API_BASE_URL environment variable is missing',
      };
    }

    const token = getTokenFromCookies();
    
    if (!token) {
      return {
        success: false,
        message: 'Authentication token not found',
        error: 'No authentication token found in cookies',
      };
    }

    const response = await fetch(`${baseUrl}/users/me`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      credentials: 'include',
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || 'Failed to fetch user information',
        error: data.error,
      };
    }

    return data as UserResponse;
  } catch (error) {
    console.error('Error fetching current user:', error);
    
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to connect to API server',
      error,
    };
  }
};

// Update user profile
export const updateUserProfile = async (
  profileData: Partial<Pick<User, 'full_name' | 'username' | 'bio'>>
): Promise<UserResponse | ApiError> => {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    
    if (!baseUrl) {
      return {
        success: false,
        message: 'API base URL is not configured',
        error: 'NEXT_PUBLIC_API_BASE_URL environment variable is missing',
      };
    }

    const token = getTokenFromCookies();
    
    if (!token) {
      return {
        success: false,
        message: 'Authentication token not found',
        error: 'No authentication token found in cookies',
      };
    }

    const response = await fetch(`${baseUrl}/users/me`, {
      method: 'PATCH',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      credentials: 'include',
      body: JSON.stringify(profileData),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || 'Failed to update user profile',
        error: data.error,
      };
    }

    return data as UserResponse;
  } catch (error) {
    console.error('Error updating user profile:', error);
    
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to connect to API server',
      error,
    };
  }
};

// Export types for use in components
export type { User, UserResponse, ApiError };