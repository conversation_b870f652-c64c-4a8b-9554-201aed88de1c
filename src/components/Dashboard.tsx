import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import ExploreTab from './ExploreTab';
import MyAgentsTab from './MyAgentsTab';
import UsageLimitationTab from './UsageLimitationTab';
import SettingsTab from './SettingsTab';
import SidePanel from './SidePanel';

const Dashboard: React.FC = () => {
  const { session, signOut } = useAuth();
  const [activeSection, setActiveSection] = useState('explore');

  const sidebarItems = [
    { id: 'explore', label: 'Explore', icon: 'fas fa-compass' },
    { id: 'my-agents', label: 'My Agents', icon: 'fas fa-robot' },
    { id: 'usage', label: 'Usage & Limitation', icon: 'fas fa-chart-bar' },
    { id: 'settings', label: 'Settings', icon: 'fas fa-cog' },
  ];

  return (
    <div className="h-screen bg-black text-white flex flex-col overflow-hidden">
      {/* Main Content */}
      <div className="flex-1 flex h-full relative">
        {/* Side Panel */}
        <SidePanel
          items={sidebarItems}
          activeSection={activeSection}
          onSectionChange={setActiveSection}
          session={session}
          onSignOut={signOut}
        />

        {/* Main Content Area */}
        <main className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto">
            {activeSection === 'explore' && <ExploreTab />}
            {activeSection === 'my-agents' && <MyAgentsTab />}
            {activeSection === 'usage' && <UsageLimitationTab />}
            {activeSection === 'settings' && <SettingsTab />}
          </div>
        </main>
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-black/95 backdrop-blur-xl border-t border-white/10 z-50 shadow-lg shadow-black/50">
        <div className="flex justify-around py-2 px-2">
          {sidebarItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setActiveSection(item.id)}
              className={`group relative flex flex-col items-center space-y-1 p-3 rounded-xl transition-all duration-300 cursor-pointer overflow-hidden ${
                activeSection === item.id
                  ? 'text-blue-400 shadow-lg shadow-blue-500/20'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              {/* Glassmorphism Background */}
              <div className={`absolute inset-0 transition-all duration-300 ${
                activeSection === item.id
                  ? 'bg-gradient-to-br from-blue-500/20 via-purple-500/15 to-cyan-500/20 backdrop-blur-lg border border-white/20'
                  : 'bg-white/5 backdrop-blur-md border border-white/10 opacity-0 group-hover:opacity-100'
              } rounded-xl`}></div>
              
              {/* Animated Glow for Active State */}
              {activeSection === item.id && (
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/25 via-purple-500/20 to-cyan-500/25 animate-pulse opacity-60"></div>
              )}
              
              {/* Content */}
              <div className="relative z-10 flex flex-col items-center space-y-1">
                <i className={`${item.icon} text-lg`}></i>
                <span className="text-xs font-medium">{item.label.split(' ')[0]}</span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard; 