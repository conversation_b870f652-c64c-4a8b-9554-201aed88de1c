import React, { useState } from 'react';

interface Agent {
  id: string;
  name: string;
  username: string;
  creator: string;
  description: string;
  avatar: string;
  coverImage: string;
  followers: number;
  interactions: number;
  createdDate: string;
  tags: string[];
  category: string;
  isVerified: boolean;
  isActive: boolean;
  lastActive: string;
  rating: number;
}

const ExploreTab: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  // Sample agents data with Instagram-like properties
  const sampleAgents: Agent[] = [
    {
      id: '1',
      name: 'CodeMaster AI',
      username: '@codemaster_ai',
      creator: '@techiedev',
      description: '🚀 Your coding companion | Expert in Python, JavaScript & React | 50k+ problems solved',
      avatar: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400',
      coverImage: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=600',
      followers: 12500,
      interactions: 45200,
      createdDate: '2024-01-15',
      tags: ['Programming', 'AI', 'Python', 'JavaScript'],
      category: 'Development',
      isVerified: true,
      isActive: true,
      lastActive: '2 minutes ago',
      rating: 4.9
    },
    {
      id: '2',
      name: 'Creative Writer',
      username: '@story_weaver',
      creator: '@authorpro',
      description: '✍️ Crafting stories that captivate | Poetry • Fiction • Screenplays | Published author',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616c88fa013?w=400',
      coverImage: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=600',
      followers: 8900,
      interactions: 23100,
      createdDate: '2024-02-20',
      tags: ['Writing', 'Creative', 'Poetry', 'Fiction'],
      category: 'Creative',
      isVerified: true,
      isActive: false,
      lastActive: '1 hour ago',
      rating: 4.7
    },
    {
      id: '3',
      name: 'Data Wizard',
      username: '@data_insights',
      creator: '@analyticsGuru',
      description: '📊 Turning data into insights | ML • Statistics • Visualization | Fortune 500 trusted',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      coverImage: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600',
      followers: 15600,
      interactions: 67800,
      createdDate: '2023-11-10',
      tags: ['Data Science', 'ML', 'Analytics', 'Python'],
      category: 'Analytics',
      isVerified: true,
      isActive: true,
      lastActive: 'Just now',
      rating: 4.8
    },
    {
      id: '4',
      name: 'Design Genius',
      username: '@pixel_perfect',
      creator: '@designstudio',
      description: '🎨 Beautiful designs that convert | UI/UX • Branding • Illustrations | Award winning',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
      coverImage: 'https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?w=600',
      followers: 9200,
      interactions: 34500,
      createdDate: '2024-03-05',
      tags: ['Design', 'UI/UX', 'Branding', 'Creative'],
      category: 'Design',
      isVerified: false,
      isActive: true,
      lastActive: '5 minutes ago',
      rating: 4.6
    },
    {
      id: '5',
      name: 'Marketing Maven',
      username: '@growth_hacker',
      creator: '@marketingpro',
      description: '📈 Growth strategies that work | SEO • Social Media • Content Marketing | 10x ROI',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
      coverImage: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600',
      followers: 18700,
      interactions: 52300,
      createdDate: '2023-12-01',
      tags: ['Marketing', 'SEO', 'Growth', 'Strategy'],
      category: 'Marketing',
      isVerified: true,
      isActive: false,
      lastActive: '3 hours ago',
      rating: 4.9
    },
    {
      id: '6',
      name: 'Health Coach AI',
      username: '@wellness_guide',
      creator: '@healthexpert',
      description: '💪 Your personal wellness companion | Nutrition • Fitness • Mental Health | Certified',
      avatar: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=400',
      coverImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600',
      followers: 11200,
      interactions: 28900,
      createdDate: '2024-01-30',
      tags: ['Health', 'Fitness', 'Nutrition', 'Wellness'],
      category: 'Health',
      isVerified: true,
      isActive: true,
      lastActive: '1 minute ago',
      rating: 4.8
    }
  ];

  const categories = ['All', 'Development', 'Creative', 'Analytics', 'Design', 'Marketing', 'Health'];

  const filteredAgents = sampleAgents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         agent.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         agent.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'All' || agent.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  return (
    <div className="min-h-full">
      {/* Content Header */}
      <div className="border-b border-white/10 bg-black/20 backdrop-blur-xl sticky top-0 z-10">
        <div className="p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            {/* Left-aligned header */}
            <div className="text-left mb-6">
              <h1 className="text-3xl lg:text-4xl font-bold mb-2 gradient-text-enhanced">Explore AI Agents</h1>
              <p className="text-gray-400 text-lg">Discover powerful AI agents built by the community</p>
            </div>
            
            {/* Search and Filter Row */}
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
              {/* Search Bar */}
              <div className="relative flex-1 max-w-md">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <i className="fas fa-search text-gray-400"></i>
                </div>
                <input
                  type="text"
                  placeholder="Search agents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200"
                />
              </div>

              {/* Category Filter */}
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      selectedCategory === category
                        ? 'bg-blue-500 text-white shadow-lg shadow-blue-500/25'
                        : 'bg-white/10 text-gray-300 hover:bg-white/20 hover:text-white'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Agents Grid */}
      <div className="p-6 lg:p-8 pb-32">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredAgents.map((agent) => (
              <div
                key={agent.id}
                className="glass-card rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 cursor-pointer group border border-white/10 hover:border-blue-500/30"
              >
                {/* Header with Avatar and Status */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center relative">
                      <i className="fas fa-robot text-white text-lg"></i>
                      {agent.isActive && (
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-black"></div>
                      )}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="text-lg font-bold text-white group-hover:text-blue-400 transition-colors duration-200">
                          {agent.name}
                        </h3>
                        {agent.isVerified && (
                          <i className="fas fa-certificate text-blue-400 text-sm"></i>
                        )}
                      </div>
                      <p className="text-gray-400 text-sm">by {agent.creator}</p>
                    </div>
                  </div>
                  
                  {/* Category Badge */}
                  <div className="px-3 py-1 bg-blue-500/20 text-blue-400 text-xs font-medium rounded-lg border border-blue-500/30">
                    {agent.category}
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-300 text-sm mb-4 leading-relaxed line-clamp-3">
                  {agent.description}
                </p>

                {/* Skills/Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {agent.tags.slice(0, 4).map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-white/5 text-gray-300 text-xs rounded-md border border-white/10 hover:bg-white/10 transition-colors"
                    >
                      {tag}
                    </span>
                  ))}
                  {agent.tags.length > 4 && (
                    <span className="px-2 py-1 bg-white/5 text-gray-400 text-xs rounded-md border border-white/10">
                      +{agent.tags.length - 4} more
                    </span>
                  )}
                </div>

                {/* Metrics Row */}
                <div className="grid grid-cols-3 gap-4 mb-4 py-3 border-t border-b border-white/10">
                  <div className="text-center">
                    <div className="text-white font-bold text-lg">{formatNumber(agent.followers)}</div>
                    <div className="text-gray-400 text-xs uppercase tracking-wide">Users</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-bold text-lg flex items-center justify-center gap-1">
                      <i className="fas fa-star text-yellow-400 text-sm"></i>
                      {agent.rating}
                    </div>
                    <div className="text-gray-400 text-xs uppercase tracking-wide">Rating</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white font-bold text-lg">{formatNumber(agent.interactions)}</div>
                    <div className="text-gray-400 text-xs uppercase tracking-wide">Chats</div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <button className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm font-medium py-3 px-4 rounded-xl transition-all duration-200 flex items-center justify-center gap-2 group">
                    <i className="fas fa-comments"></i>
                    <span>Start Chat</span>
                  </button>
                  <button className="w-12 h-12 bg-white/10 hover:bg-white/20 text-gray-300 hover:text-white rounded-xl transition-colors duration-200 flex items-center justify-center group">
                    <i className="fas fa-bookmark group-hover:scale-110 transition-transform"></i>
                  </button>
                </div>

                {/* Footer Info */}
                <div className="mt-4 pt-3 border-t border-white/10 flex items-center justify-between text-xs">
                  <span className="text-gray-400">
                    <i className="fas fa-clock mr-1"></i>
                    Last active {agent.lastActive}
                  </span>
                  <span className="text-gray-400">
                    Created {new Date(agent.createdDate).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Load More */}
          {filteredAgents.length > 0 && (
            <div className="mt-12 text-center">
              <button className="glass-card px-8 py-4 rounded-2xl text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-200 border border-white/10 cursor-pointer group">
                <i className="fas fa-plus mr-3 group-hover:rotate-90 transition-transform duration-200"></i>
                Load More Agents
              </button>
            </div>
          )}

          {/* No Results */}
          {filteredAgents.length === 0 && (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-search text-gray-400 text-3xl"></i>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">No agents found</h3>
              <p className="text-gray-400 mb-6">Try adjusting your search or filter criteria</p>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('All');
                }}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-xl transition-colors duration-200"
              >
                Clear Filters
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExploreTab;