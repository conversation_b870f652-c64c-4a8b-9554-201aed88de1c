import React, { useState } from 'react';

// Enumerations as specified in the requirements
enum ProviderType {
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE', 
  AZURE = 'AZURE',
  HUGGING_FACE = 'HUGGING_FACE',
  COHERE = 'COHERE',
  MISTRAL = 'MISTRAL',
  OLLAMA = 'OLLAMA',
  GROQ = 'GROQ',
  PERPLEXITY = 'PERPLEXITY',
  CEREBRAS = 'CEREBRAS',
  LITELLM = 'LITELLM'
}

enum ModelType {
  CHAT = 'CHAT',
  COMPLETION = 'COMPLETION',
  EMBEDDING = 'EMBEDDING',
  IMAGE_GENERATION = 'IMAGE_GENERATION',
  TEXT_TO_SPEECH = 'TEXT_TO_SPEECH',
  SPEECH_TO_TEXT = 'SPEECH_TO_TEXT',
  REASONING = 'REASONING'
}

interface Provider {
  type: ProviderType;
  name: string;
  description: string;
  apiKey: string;
  isEnabled: boolean;
  icon: string;
  supportedModels: ModelType[];
  website: string;
  color: string;
}

interface Model {
  id: string;
  name: string;
  provider: ProviderType;
  type: ModelType;
  description: string;
  isEnabled: boolean;
  maxTokens?: number;
  costPer1kTokens?: number;
}

const ModelsTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'providers' | 'models' | 'keys'>('providers');
  
  const [providers, setProviders] = useState<Provider[]>([
    {
      type: ProviderType.OPENAI,
      name: 'OpenAI',
      description: 'Advanced AI models including GPT-4 and GPT-3.5',
      apiKey: '',
      isEnabled: false,
      icon: 'fas fa-brain',
      supportedModels: [ModelType.CHAT, ModelType.COMPLETION, ModelType.EMBEDDING, ModelType.TEXT_TO_SPEECH, ModelType.SPEECH_TO_TEXT],
      website: 'https://openai.com',
      color: '#10a37f'
    },
    {
      type: ProviderType.ANTHROPIC,
      name: 'Anthropic',
      description: 'Claude family of AI models for safe and helpful AI',
      apiKey: '',
      isEnabled: false,
      icon: 'fas fa-shield-alt',
      supportedModels: [ModelType.CHAT, ModelType.COMPLETION, ModelType.REASONING],
      website: 'https://anthropic.com',
      color: '#c46d39'
    },
    {
      type: ProviderType.GOOGLE,
      name: 'Google AI',
      description: 'Gemini models and Google AI services',
      apiKey: '',
      isEnabled: false,
      icon: 'fab fa-google',
      supportedModels: [ModelType.CHAT, ModelType.COMPLETION, ModelType.EMBEDDING, ModelType.IMAGE_GENERATION],
      website: 'https://ai.google.dev',
      color: '#4285f4'
    },
    {
      type: ProviderType.GROQ,
      name: 'Groq',
      description: 'Ultra-fast inference for language models',
      apiKey: '',
      isEnabled: false,
      icon: 'fas fa-rocket',
      supportedModels: [ModelType.CHAT, ModelType.COMPLETION],
      website: 'https://groq.com',
      color: '#ff6b6b'
    },
    {
      type: ProviderType.HUGGING_FACE,
      name: 'Hugging Face',
      description: 'Open-source machine learning models and datasets',
      apiKey: '',
      isEnabled: false,
      icon: 'fas fa-heart',
      supportedModels: [ModelType.CHAT, ModelType.COMPLETION, ModelType.EMBEDDING, ModelType.IMAGE_GENERATION, ModelType.TEXT_TO_SPEECH],
      website: 'https://huggingface.co',
      color: '#ffce54'
    },
    {
      type: ProviderType.COHERE,
      name: 'Cohere',
      description: 'Enterprise-ready large language models',
      apiKey: '',
      isEnabled: false,
      icon: 'fas fa-cog',
      supportedModels: [ModelType.CHAT, ModelType.COMPLETION, ModelType.EMBEDDING],
      website: 'https://cohere.ai',
      color: '#39c5bb'
    },
    {
      type: ProviderType.MISTRAL,
      name: 'Mistral AI',
      description: 'Efficient and powerful open language models',
      apiKey: '',
      isEnabled: false,
      icon: 'fas fa-wind',
      supportedModels: [ModelType.CHAT, ModelType.COMPLETION],
      website: 'https://mistral.ai',
      color: '#ff7675'
    },
    {
      type: ProviderType.OLLAMA,
      name: 'Ollama',
      description: 'Run large language models locally',
      apiKey: '',
      isEnabled: false,
      icon: 'fas fa-server',
      supportedModels: [ModelType.CHAT, ModelType.COMPLETION, ModelType.EMBEDDING],
      website: 'https://ollama.ai',
      color: '#6c5ce7'
    }
  ]);

  const [models, setModels] = useState<Model[]>([
    {
      id: 'gpt-4o',
      name: 'GPT-4o',
      provider: ProviderType.OPENAI,
      type: ModelType.CHAT,
      description: 'Most capable GPT-4 model with multimodal capabilities',
      isEnabled: false,
      maxTokens: 128000,
      costPer1kTokens: 0.03
    },
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      provider: ProviderType.OPENAI,
      type: ModelType.CHAT,
      description: 'Fast and efficient model for most tasks',
      isEnabled: false,
      maxTokens: 16385,
      costPer1kTokens: 0.002
    },
    {
      id: 'claude-3-opus',
      name: 'Claude 3 Opus',
      provider: ProviderType.ANTHROPIC,
      type: ModelType.CHAT,
      description: 'Most powerful Claude model for complex tasks',
      isEnabled: false,
      maxTokens: 200000,
      costPer1kTokens: 0.075
    },
    {
      id: 'gemini-pro',
      name: 'Gemini Pro',
      provider: ProviderType.GOOGLE,
      type: ModelType.CHAT,
      description: 'Google\'s most capable AI model',
      isEnabled: false,
      maxTokens: 32768,
      costPer1kTokens: 0.0025
    }
  ]);

  const handleProviderToggle = (providerType: ProviderType) => {
    setProviders(prev => prev.map(provider => 
      provider.type === providerType 
        ? { ...provider, isEnabled: !provider.isEnabled }
        : provider
    ));
  };

  const handleApiKeyChange = (providerType: ProviderType, apiKey: string) => {
    setProviders(prev => prev.map(provider => 
      provider.type === providerType 
        ? { ...provider, apiKey }
        : provider
    ));
  };

  const handleModelToggle = (modelId: string) => {
    setModels(prev => prev.map(model => 
      model.id === modelId 
        ? { ...model, isEnabled: !model.isEnabled }
        : model
    ));
  };

  const getModelTypeIcon = (type: ModelType) => {
    switch (type) {
      case ModelType.CHAT: return 'fas fa-comments';
      case ModelType.COMPLETION: return 'fas fa-edit';
      case ModelType.EMBEDDING: return 'fas fa-vector-square';
      case ModelType.IMAGE_GENERATION: return 'fas fa-image';
      case ModelType.TEXT_TO_SPEECH: return 'fas fa-volume-up';
      case ModelType.SPEECH_TO_TEXT: return 'fas fa-microphone';
      case ModelType.REASONING: return 'fas fa-brain';
      default: return 'fas fa-cog';
    }
  };

  const enabledProviders = providers.filter(p => p.isEnabled);
  const enabledModels = models.filter(m => m.isEnabled);

  return (
    <div className="min-h-full">
      {/* Content Header */}
      <div className="border-b border-white/10 bg-black/20 backdrop-blur-xl sticky top-0 z-10">
        <div className="p-8">
          <div className="max-w-6xl">
            {/* Header with Stats */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <h1 className="text-4xl font-bold mb-2">AI Models</h1>
                <p className="text-gray-400">Configure AI providers and manage model settings</p>
              </div>
              
              {/* Stats moved to top right */}
              <div className="flex gap-4">
                <div className="glass-card rounded-xl p-4 border border-white/10">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-green-500/20 flex items-center justify-center">
                      <i className="fas fa-check text-green-400"></i>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-white">{enabledProviders.length}</div>
                      <div className="text-sm text-gray-400">Active Providers</div>
                    </div>
                  </div>
                </div>
                <div className="glass-card rounded-xl p-4 border border-white/10">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center">
                      <i className="fas fa-robot text-blue-400"></i>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-white">{enabledModels.length}</div>
                      <div className="text-sm text-gray-400">Available Models</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Tab Navigation */}
            <div className="flex space-x-1">
              <button
                onClick={() => setActiveTab('providers')}
                className={`px-6 py-2 rounded-full transition-all duration-200 text-sm font-medium ${
                  activeTab === 'providers'
                    ? 'bg-white/10 text-white border border-white/20'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                Providers
              </button>
              <button
                onClick={() => setActiveTab('models')}
                className={`px-6 py-2 rounded-full transition-all duration-200 text-sm font-medium ${
                  activeTab === 'models'
                    ? 'bg-white/10 text-white border border-white/20'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                Models
              </button>
              <button
                onClick={() => setActiveTab('keys')}
                className={`px-6 py-2 rounded-full transition-all duration-200 text-sm font-medium ${
                  activeTab === 'keys'
                    ? 'bg-white/10 text-white border border-white/20'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                API Keys
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-8 pb-32">
        <div className="max-w-6xl">
          {activeTab === 'providers' && (
            <div>
              <h2 className="text-2xl font-semibold text-white mb-6">AI Providers</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {providers.map((provider) => (
                  <div key={provider.type} className="glass-card rounded-2xl p-6 border border-white/10 hover:bg-white/5 transition-all duration-300">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-12 h-12 rounded-xl flex items-center justify-center text-white text-xl"
                          style={{ backgroundColor: `${provider.color}20`, border: `1px solid ${provider.color}40` }}
                        >
                          <i className={provider.icon} style={{ color: provider.color }}></i>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">{provider.name}</h3>
                          <p className="text-sm text-gray-400">{provider.description}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleProviderToggle(provider.type)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          provider.isEnabled ? 'bg-blue-600' : 'bg-gray-600'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            provider.isEnabled ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>

                    <div className="mb-4">
                      <div className="flex flex-wrap gap-2">
                        {provider.supportedModels.map((modelType) => (
                          <span 
                            key={modelType} 
                            className="px-2 py-1 bg-white/10 text-gray-300 rounded-md text-xs font-medium border border-white/20 flex items-center gap-1"
                          >
                            <i className={getModelTypeIcon(modelType)} style={{ fontSize: '10px' }}></i>
                            {modelType.replace('_', ' ')}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <a 
                        href={provider.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 text-sm transition-colors duration-200"
                      >
                        Visit Website
                      </a>
                      <div className={`px-2 py-1 rounded-md text-xs font-medium ${
                        provider.isEnabled 
                          ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                          : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
                      }`}>
                        {provider.isEnabled ? 'Enabled' : 'Disabled'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'models' && (
            <div>
              <h2 className="text-2xl font-semibold text-white mb-6">Available Models</h2>
              
              <div className="space-y-4">
                {models.map((model) => {
                  const provider = providers.find(p => p.type === model.provider);
                  return (
                    <div key={model.id} className="glass-card rounded-2xl p-6 border border-white/10 hover:bg-white/5 transition-all duration-300">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 flex-1">
                          <div 
                            className="w-12 h-12 rounded-xl flex items-center justify-center text-white text-lg"
                            style={{ backgroundColor: `${provider?.color}20`, border: `1px solid ${provider?.color}40` }}
                          >
                            <i className={getModelTypeIcon(model.type)} style={{ color: provider?.color }}></i>
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-1">
                              <h3 className="text-xl font-semibold text-white">{model.name}</h3>
                              <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-md text-xs font-medium border border-blue-500/30">
                                {provider?.name}
                              </span>
                              <span className="px-2 py-1 bg-purple-500/20 text-purple-300 rounded-md text-xs font-medium border border-purple-500/30">
                                {model.type.replace('_', ' ')}
                              </span>
                            </div>
                            <p className="text-gray-400 text-sm mb-2">{model.description}</p>
                            <div className="flex gap-4 text-sm text-gray-400">
                              {model.maxTokens && (
                                <span>Max Tokens: {model.maxTokens.toLocaleString()}</span>
                              )}
                              {model.costPer1kTokens && (
                                <span>Cost: ${model.costPer1kTokens}/1k tokens</span>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <button
                          onClick={() => handleModelToggle(model.id)}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            model.isEnabled ? 'bg-blue-600' : 'bg-gray-600'
                          }`}
                          disabled={!provider?.isEnabled}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              model.isEnabled ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'keys' && (
            <div>
              <h2 className="text-2xl font-semibold text-white mb-6">API Keys Configuration</h2>
              <p className="text-gray-400 mb-8">Configure your API keys to enable AI providers. Keys are stored securely and encrypted.</p>
              
              <div className="space-y-6">
                {providers.map((provider) => (
                  <div key={provider.type} className="glass-card rounded-2xl p-6 border border-white/10">
                    <div className="flex items-center gap-4 mb-4">
                      <div 
                        className="w-12 h-12 rounded-xl flex items-center justify-center text-white text-xl"
                        style={{ backgroundColor: `${provider.color}20`, border: `1px solid ${provider.color}40` }}
                      >
                        <i className={provider.icon} style={{ color: provider.color }}></i>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white">{provider.name}</h3>
                        <p className="text-sm text-gray-400">Configure API key for {provider.name}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                      <div className="md:col-span-2">
                        <label className="block text-white font-medium mb-2">API Key</label>
                        <input
                          type="password"
                          value={provider.apiKey}
                          onChange={(e) => handleApiKeyChange(provider.type, e.target.value)}
                          placeholder={`Enter your ${provider.name} API key`}
                          className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200"
                        />
                      </div>
                      <div className="flex gap-2">
                        <button 
                          onClick={() => handleProviderToggle(provider.type)}
                          className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                            provider.isEnabled
                              ? 'bg-red-600 hover:bg-red-700 text-white'
                              : 'bg-blue-600 hover:bg-blue-700 text-white'
                          }`}
                          disabled={!provider.apiKey}
                        >
                          {provider.isEnabled ? 'Disable' : 'Enable'}
                        </button>
                        <button className="px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-xl font-medium transition-all duration-200">
                          Test
                        </button>
                      </div>
                    </div>

                    {provider.apiKey && (
                      <div className="mt-4 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                        <div className="flex items-center gap-2 text-green-400 text-sm">
                          <i className="fas fa-check-circle"></i>
                          <span>API key configured</span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModelsTab;