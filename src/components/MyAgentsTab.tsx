import React, { useState } from 'react';

interface UserAgent {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'training';
  createdDate: string;
  lastModified: string;
  conversationsCount: number;
  isFavorite: boolean;
  model: string;
  tools: string[];
}

const MyAgentsTab: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchMinimized, setIsSearchMinimized] = useState(false);
  const [agents, setAgents] = useState<UserAgent[]>([
    {
      id: '1',
      name: 'Customer Support Bot',
      description: 'Handles customer inquiries and support tickets with advanced AI capabilities',
      status: 'active',
      createdDate: '15.01.2024',
      lastModified: '2024-01-20',
      conversationsCount: 156,
      isFavorite: true,
      model: 'GPT4o',
      tools: ['Tools', 'Search']
    },
    {
      id: '2',
      name: 'Sales Assistant',
      description: 'Helps with product recommendations and sales automation',
      status: 'active',
      createdDate: '10.01.2024',
      lastModified: '2024-01-18',
      conversationsCount: 89,
      isFavorite: true,
      model: 'GPT4o',
      tools: ['Tools']
    },
    {
      id: '3',
      name: 'Content Writer',
      description: 'Creates engaging content for marketing and social media campaigns',
      status: 'inactive',
      createdDate: '08.01.2024',
      lastModified: '2024-01-15',
      conversationsCount: 45,
      isFavorite: false,
      model: 'GPT4o',
      tools: ['Tools', 'Writing']
    },
    {
      id: '4',
      name: 'Data Analyst',
      description: 'Analyzes business data and generates comprehensive reports',
      status: 'active',
      createdDate: '05.01.2024',
      lastModified: '2024-01-12',
      conversationsCount: 78,
      isFavorite: false,
      model: 'GPT4o',
      tools: ['Tools', 'Analytics']
    }
  ]);

  const favoriteAgents = agents.filter(agent => agent.isFavorite);
  const filteredAgents = agents.filter(agent => 
    agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    agent.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCreateAgent = () => {
    // TODO: Implement create agent functionality
    console.log('Create new agent clicked');
  };

  const toggleFavorite = (agentId: string) => {
    setAgents(prev => prev.map(agent => 
      agent.id === agentId ? { ...agent, isFavorite: !agent.isFavorite } : agent
    ));
  };

  return (
    <div className="min-h-full bg-black text-white">
      {/* Header Section */}
      <div className="border-b border-white/10 bg-black/20 backdrop-blur-xl sticky top-0 z-10">
        <div className={`transition-all duration-300 ${isSearchMinimized ? 'p-4' : 'p-8'}`}>
          <div className="max-w-6xl mx-auto">
            {/* Title and Create Button */}
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-4">
                <h1 className={`font-bold text-white transition-all duration-300 ${isSearchMinimized ? 'text-2xl' : 'text-4xl'}`}>
                  {isSearchMinimized ? 'Agents' : 'Search Your Agent'}
                </h1>
                <button
                  onClick={() => setIsSearchMinimized(!isSearchMinimized)}
                  className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                  title={isSearchMinimized ? 'Expand search' : 'Minimize search'}
                >
                  <i className={`fas ${isSearchMinimized ? 'fa-chevron-down' : 'fa-chevron-up'} text-sm`}></i>
                </button>
              </div>
              <button
                onClick={handleCreateAgent}
                className="px-6 py-3 bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 rounded-2xl text-white font-medium transition-all duration-300 backdrop-blur-lg"
              >
                Create New Agent
              </button>
            </div>

            {/* Search Bar */}
            <div className={`transition-all duration-300 overflow-hidden ${isSearchMinimized ? 'max-h-0 opacity-0 mb-0' : 'max-h-32 opacity-100 mb-6'}`}>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                  <svg className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search agents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-16 pr-6 py-4 text-lg bg-white/5 border-2 border-white/10 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-400/50 focus:bg-white/10 transition-all duration-300 backdrop-blur-lg"
                />
              </div>
            </div>

            {/* Quick Search when minimized */}
            {isSearchMinimized && searchQuery && (
              <div className="mb-4">
                <div className="flex items-center gap-2 text-sm text-gray-400">
                  <i className="fas fa-search"></i>
                  <span>Searching for: "{searchQuery}"</span>
                  <button
                    onClick={() => setSearchQuery('')}
                    className="text-gray-500 hover:text-white ml-2"
                  >
                    <i className="fas fa-times"></i>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-8 pb-32">
        <div className="max-w-6xl mx-auto space-y-12">
          
          {/* Favourite Agents Section */}
          {favoriteAgents.length > 0 && (
            <div>
              <div className="flex items-center gap-3 mb-6">
                <div className="flex items-center justify-center w-8 h-8">
                  <svg className="w-6 h-6 text-red-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                  </svg>
                </div>
                <h2 className="text-2xl font-semibold text-white">Favourite Agents</h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                {favoriteAgents.map((agent) => (
                  <div key={agent.id} className="glass-card rounded-2xl p-6 border border-white/10 hover:bg-white/5 transition-all duration-300 group">
                    <div className="flex flex-col items-center text-center">
                      {/* Agent Avatar */}
                      <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center mb-4 group-hover:scale-105 transition-transform duration-300">
                        <i className="fas fa-robot text-white text-xl"></i>
                      </div>
                      
                      {/* Agent Name */}
                      <h3 className="text-xl font-semibold text-white mb-2">{agent.name}</h3>
                      
                      {/* Agent Description */}
                      <p className="text-gray-400 text-sm mb-4 line-clamp-2">{agent.description}</p>
                      
                      {/* Tags */}
                      <div className="flex flex-wrap gap-2 justify-center">
                        <span className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs font-medium border border-blue-500/30">
                          {agent.model}
                        </span>
                        {agent.tools.map((tool, index) => (
                          <span key={index} className="px-3 py-1 bg-gray-500/20 text-gray-300 rounded-full text-xs font-medium border border-gray-500/30">
                            {tool}
                          </span>
                        ))}
                      </div>
                      
                      {/* Favorite Button */}
                      <button
                        onClick={() => toggleFavorite(agent.id)}
                        className="absolute top-4 right-4 p-2 text-red-400 hover:text-red-300 transition-colors duration-200"
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* All Bots Section */}
          <div>
            <div className="flex items-center gap-3 mb-6">
              <div className="flex items-center justify-center w-8 h-8">
                <svg className="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H9C7.9 1 7 1.9 7 3V7C7 8.1 7.9 9 9 9M7 11C5.9 11 5 11.9 5 13V19C5 20.1 5.9 21 7 21H17C18.1 21 19 20.1 19 19V13C19 11.9 18.1 11 17 11H7Z"/>
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-white">All Bots</h2>
            </div>
            
            <div className="space-y-4">
              {filteredAgents.map((agent) => (
                <div key={agent.id} className="glass-card rounded-2xl p-6 border border-white/10 hover:bg-white/5 transition-all duration-300 group">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 flex-1">
                      {/* Agent Avatar */}
                      <div className="w-14 h-14 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                        <i className="fas fa-robot text-white text-lg"></i>
                      </div>
                      
                      <div className="flex-1">
                        {/* Agent Name */}
                        <h3 className="text-xl font-semibold text-white mb-1">{agent.name}</h3>
                        {/* Agent Description */}
                        <p className="text-gray-400 text-sm mb-2">{agent.description}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-6">
                      {/* Tags */}
                      <div className="flex flex-wrap gap-2">
                        <span className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs font-medium border border-blue-500/30">
                          {agent.model}
                        </span>
                        {agent.tools.map((tool, index) => (
                          <span key={index} className="px-3 py-1 bg-gray-500/20 text-gray-300 rounded-full text-xs font-medium border border-gray-500/30">
                            {tool}
                          </span>
                        ))}
                      </div>
                      
                      {/* Created Date */}
                      <div className="text-right">
                        <p className="text-gray-400 text-sm">Created on</p>
                        <p className="text-white text-sm font-medium">{agent.createdDate}</p>
                      </div>
                      
                      {/* Favorite Button */}
                      <button
                        onClick={() => toggleFavorite(agent.id)}
                        className={`p-2 transition-colors duration-200 ${
                          agent.isFavorite 
                            ? 'text-red-400 hover:text-red-300' 
                            : 'text-gray-500 hover:text-red-400'
                        }`}
                      >
                        <svg className="w-5 h-5" fill={agent.isFavorite ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredAgents.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 rounded-full bg-gray-700/50 flex items-center justify-center mx-auto mb-4">
                  <i className="fas fa-robot text-gray-400 text-xl"></i>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">No agents found</h3>
                <p className="text-gray-400 mb-6">
                  {searchQuery ? 'Try adjusting your search terms' : 'Create your first AI agent to get started'}
                </p>
                <button 
                  onClick={handleCreateAgent}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl transition-all duration-200"
                >
                  Create Your First Agent
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyAgentsTab;