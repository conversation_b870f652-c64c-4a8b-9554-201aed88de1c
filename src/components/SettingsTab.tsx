import React, { useState, useEffect } from 'react';
import { getCurrentUser, updateUserProfile, User, UserResponse, ApiError } from '../api/usersApi';

interface UserProfile {
  id: string;
  full_name: string;
  email: string;
  username: string;
  avatar_url?: string | null;
  bio?: string | null;
  auth_provider: string;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
  timezone: string;
  language: string;
  company?: string;
  role?: string;
}

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  agentAlerts: boolean;
  billingAlerts: boolean;
  securityAlerts: boolean;
  marketingEmails: boolean;
}

interface SecuritySettings {
  twoFactorAuth: boolean;
  sessionTimeout: number;
  loginAlerts: boolean;
}

const SettingsTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'profile' | 'notifications' | 'security' | 'preferences'>('profile');
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);

  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: true,
    agentAlerts: true,
    billingAlerts: true,
    securityAlerts: true,
    marketingEmails: false
  });

  const [security, setSecurity] = useState<SecuritySettings>({
    twoFactorAuth: false,
    sessionTimeout: 30,
    loginAlerts: true
  });

  const handleNotificationChange = (key: keyof NotificationSettings) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Fetch user data on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await getCurrentUser();
        
        if (response.success) {
          const userData = (response as UserResponse).data.user;
          setProfile({
            id: userData.id,
            full_name: userData.full_name,
            email: userData.email,
            username: userData.username,
            avatar_url: userData.avatar_url,
            bio: userData.bio,
            auth_provider: userData.auth_provider,
            is_verified: userData.is_verified,
            created_at: userData.created_at,
            updated_at: userData.updated_at,
            timezone: 'UTC+5:30 (IST)', // Default timezone
            language: 'English', // Default language
            company: '',
            role: ''
          });
        } else {
          setError((response as ApiError).message);
        }
      } catch (err) {
        setError('Failed to load user data');
        console.error('Error fetching user data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const handleSecurityChange = (key: keyof SecuritySettings, value: boolean | number) => {
    setSecurity(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveProfile = async () => {
    if (!profile) return;
    
    setSaving(true);
    setSaveSuccess(false);
    setError(null);

    try {
      const response = await updateUserProfile({
        full_name: profile.full_name,
        username: profile.username,
        bio: profile.bio || undefined,
      });

      if (response.success) {
        setSaveSuccess(true);
        setTimeout(() => setSaveSuccess(false), 3000);
      } else {
        setError((response as ApiError).message);
      }
    } catch (err) {
      setError('Failed to save changes');
      console.error('Error saving profile:', err);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="min-h-full">
      {/* Content Header */}
      <div className="border-b border-white/10 bg-black/20 backdrop-blur-xl sticky top-0 z-10">
        <div className="p-8">
          <div className="max-w-4xl">
            <h1 className="text-4xl font-bold mb-2">Settings</h1>
            <p className="text-gray-400">Manage your account and preferences</p>
            
            {/* Tab Navigation */}
            <div className="flex space-x-1 mt-6">
              <button
                onClick={() => setActiveTab('profile')}
                className={`px-6 py-2 rounded-full transition-all duration-200 text-sm font-medium ${
                  activeTab === 'profile'
                    ? 'bg-white/10 text-white border border-white/20'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                Profile
              </button>
              <button
                onClick={() => setActiveTab('notifications')}
                className={`px-6 py-2 rounded-full transition-all duration-200 text-sm font-medium ${
                  activeTab === 'notifications'
                    ? 'bg-white/10 text-white border border-white/20'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                Notifications
              </button>
              <button
                onClick={() => setActiveTab('security')}
                className={`px-6 py-2 rounded-full transition-all duration-200 text-sm font-medium ${
                  activeTab === 'security'
                    ? 'bg-white/10 text-white border border-white/20'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                Security
              </button>
              <button
                onClick={() => setActiveTab('preferences')}
                className={`px-6 py-2 rounded-full transition-all duration-200 text-sm font-medium ${
                  activeTab === 'preferences'
                    ? 'bg-white/10 text-white border border-white/20'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                Preferences
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-8 pb-32">
        <div className="max-w-4xl">
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center space-x-4 text-white">
                <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span className="text-lg">Loading profile...</span>
              </div>
            </div>
          )}

          {error && (
            <div className="glass-card rounded-2xl p-6 border border-red-500/20 bg-red-500/10 mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
                <div>
                  <h3 className="text-red-400 font-medium">Error</h3>
                  <p className="text-red-300 text-sm">{error}</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'profile' && profile && (
            <div>
              <h2 className="text-2xl font-semibold text-white mb-6">Profile Information</h2>
              
              {saveSuccess && (
                <div className="glass-card rounded-2xl p-4 border border-green-500/20 bg-green-500/10 mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <p className="text-green-400 font-medium">Profile updated successfully!</p>
                  </div>
                </div>
              )}
              
              <div className="glass-card rounded-2xl p-6 border border-white/10 mb-6">
                <div className="flex items-center space-x-6 mb-6">
                  <div className="w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center overflow-hidden">
                    {profile.avatar_url ? (
                      <img
                        src={profile.avatar_url}
                        alt="Profile"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-white font-bold text-2xl">
                        {profile.full_name.split(' ').map((n: string) => n[0]).join('')}
                      </span>
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white">{profile.full_name}</h3>
                    <p className="text-gray-400">{profile.email}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        profile.is_verified ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                      }`}>
                        {profile.is_verified ? 'Verified' : 'Unverified'}
                      </span>
                      <span className="px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-400 capitalize">
                        {profile.auth_provider}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-white font-medium mb-2">Full Name</label>
                    <input
                      type="text"
                      value={profile.full_name}
                      onChange={(e) => setProfile(prev => prev ? ({ ...prev, full_name: e.target.value }) : null)}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200"
                    />
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-2">Username</label>
                    <input
                      type="text"
                      value={profile.username}
                      onChange={(e) => setProfile(prev => prev ? ({ ...prev, username: e.target.value }) : null)}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200"
                    />
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-2">Email</label>
                    <input
                      type="email"
                      value={profile.email}
                      disabled
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-gray-400 placeholder-gray-400 cursor-not-allowed"
                      title="Email cannot be changed"
                    />
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-2">Bio</label>
                    <textarea
                      value={profile.bio || ''}
                      onChange={(e) => setProfile(prev => prev ? ({ ...prev, bio: e.target.value }) : null)}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200 resize-none h-24"
                      placeholder="Tell us about yourself..."
                    />
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-2">Company</label>
                    <input
                      type="text"
                      value={profile.company || ''}
                      onChange={(e) => setProfile(prev => prev ? ({ ...prev, company: e.target.value }) : null)}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200"
                      placeholder="Your company name"
                    />
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-2">Role</label>
                    <input
                      type="text"
                      value={profile.role || ''}
                      onChange={(e) => setProfile(prev => prev ? ({ ...prev, role: e.target.value }) : null)}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200"
                      placeholder="Your role"
                    />
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-2">Timezone</label>
                    <select
                      value={profile.timezone}
                      onChange={(e) => setProfile(prev => prev ? ({ ...prev, timezone: e.target.value }) : null)}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200"
                    >
                      <option value="UTC+5:30 (IST)">UTC+5:30 (IST)</option>
                      <option value="UTC-8 (Pacific Time)">UTC-8 (Pacific Time)</option>
                      <option value="UTC-5 (Eastern Time)">UTC-5 (Eastern Time)</option>
                      <option value="UTC+0 (GMT)">UTC+0 (GMT)</option>
                      <option value="UTC+1 (CET)">UTC+1 (CET)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-white font-medium mb-2">Language</label>
                    <select
                      value={profile.language}
                      onChange={(e) => setProfile(prev => prev ? ({ ...prev, language: e.target.value }) : null)}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200"
                    >
                      <option value="English">English</option>
                      <option value="Spanish">Spanish</option>
                      <option value="French">French</option>
                      <option value="German">German</option>
                    </select>
                  </div>
                </div>

                <div className="flex justify-between items-center mt-6">
                  <div className="text-sm text-gray-400">
                    Account created: {new Date(profile.created_at).toLocaleDateString()}
                  </div>
                  <button
                    onClick={handleSaveProfile}
                    disabled={saving}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl transition-all duration-200 flex items-center space-x-2"
                  >
                    {saving && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
                    <span>{saving ? 'Saving...' : 'Save Changes'}</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div>
              <h2 className="text-2xl font-semibold text-white mb-6">Notification Preferences</h2>
              
              <div className="space-y-6">
                <div className="glass-card rounded-2xl p-6 border border-white/10">
                  <h3 className="text-xl font-semibold text-white mb-4">General Notifications</h3>
                  <div className="space-y-4">
                    {[
                      { key: 'emailNotifications', label: 'Email Notifications', description: 'Receive notifications via email' },
                      { key: 'pushNotifications', label: 'Push Notifications', description: 'Receive browser push notifications' }
                    ].map((item) => (
                      <div key={item.key} className="flex items-center justify-between">
                        <div>
                          <h4 className="text-white font-medium">{item.label}</h4>
                          <p className="text-gray-400 text-sm">{item.description}</p>
                        </div>
                        <button
                          onClick={() => handleNotificationChange(item.key as keyof NotificationSettings)}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            notifications[item.key as keyof NotificationSettings] ? 'bg-blue-600' : 'bg-gray-600'
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              notifications[item.key as keyof NotificationSettings] ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="glass-card rounded-2xl p-6 border border-white/10">
                  <h3 className="text-xl font-semibold text-white mb-4">Alert Types</h3>
                  <div className="space-y-4">
                    {[
                      { key: 'agentAlerts', label: 'Agent Alerts', description: 'Notifications about agent activities and errors' },
                      { key: 'billingAlerts', label: 'Billing Alerts', description: 'Notifications about billing and usage limits' },
                      { key: 'securityAlerts', label: 'Security Alerts', description: 'Important security and login notifications' },
                      { key: 'marketingEmails', label: 'Marketing Emails', description: 'Product updates and promotional content' }
                    ].map((item) => (
                      <div key={item.key} className="flex items-center justify-between">
                        <div>
                          <h4 className="text-white font-medium">{item.label}</h4>
                          <p className="text-gray-400 text-sm">{item.description}</p>
                        </div>
                        <button
                          onClick={() => handleNotificationChange(item.key as keyof NotificationSettings)}
                          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                            notifications[item.key as keyof NotificationSettings] ? 'bg-blue-600' : 'bg-gray-600'
                          }`}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              notifications[item.key as keyof NotificationSettings] ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div>
              <h2 className="text-2xl font-semibold text-white mb-6">Security Settings</h2>
              
              <div className="space-y-6">
                <div className="glass-card rounded-2xl p-6 border border-white/10">
                  <h3 className="text-xl font-semibold text-white mb-4">Authentication</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-white font-medium">Two-Factor Authentication</h4>
                        <p className="text-gray-400 text-sm">Add an extra layer of security to your account</p>
                      </div>
                      <button
                        onClick={() => handleSecurityChange('twoFactorAuth', !security.twoFactorAuth)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          security.twoFactorAuth ? 'bg-blue-600' : 'bg-gray-600'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            security.twoFactorAuth ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-white font-medium">Login Alerts</h4>
                        <p className="text-gray-400 text-sm">Get notified of new login attempts</p>
                      </div>
                      <button
                        onClick={() => handleSecurityChange('loginAlerts', !security.loginAlerts)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          security.loginAlerts ? 'bg-blue-600' : 'bg-gray-600'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            security.loginAlerts ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="glass-card rounded-2xl p-6 border border-white/10">
                  <h3 className="text-xl font-semibold text-white mb-4">Session Management</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-white font-medium mb-2">Session Timeout (minutes)</label>
                      <select
                        value={security.sessionTimeout}
                        onChange={(e) => handleSecurityChange('sessionTimeout', parseInt(e.target.value))}
                        className="w-full max-w-xs px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200"
                      >
                        <option value={15}>15 minutes</option>
                        <option value={30}>30 minutes</option>
                        <option value={60}>1 hour</option>
                        <option value={240}>4 hours</option>
                        <option value={480}>8 hours</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="glass-card rounded-2xl p-6 border border-white/10">
                  <h3 className="text-xl font-semibold text-white mb-4">Password</h3>
                  <div className="space-y-4">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl transition-all duration-200">
                      Change Password
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div>
              <h2 className="text-2xl font-semibold text-white mb-6">Application Preferences</h2>
              
              <div className="space-y-6">
                <div className="glass-card rounded-2xl p-6 border border-white/10">
                  <h3 className="text-xl font-semibold text-white mb-4">Appearance</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-white font-medium mb-2">Theme</label>
                      <div className="flex space-x-4">
                        <button className="px-4 py-2 bg-white/10 text-white border border-white/20 rounded-lg">
                          Dark (Current)
                        </button>
                        <button className="px-4 py-2 text-gray-400 hover:text-white hover:bg-white/5 rounded-lg transition-all duration-200">
                          Light (Coming Soon)
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="glass-card rounded-2xl p-6 border border-white/10">
                  <h3 className="text-xl font-semibold text-white mb-4">Data & Privacy</h3>
                  <div className="space-y-4">
                    <button className="text-blue-400 hover:text-blue-300 transition-colors duration-200">
                      Download Your Data
                    </button>
                    <br />
                    <button className="text-red-400 hover:text-red-300 transition-colors duration-200">
                      Delete Account
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsTab; 