import React from 'react';
import Image from 'next/image';
import { useAppSettings } from '@/contexts/AppSettingsContext';
import { Session } from '@/types/auth';

interface SidebarItem {
  id: string;
  label: string;
  icon: string;
}

interface SidePanelProps {
  items: SidebarItem[];
  activeSection: string;
  onSectionChange: (section: string) => void;
  className?: string;
  session?: Session | null;
  onSignOut?: () => void;
}

const SidePanel: React.FC<SidePanelProps> = ({
  items,
  activeSection,
  onSectionChange,
  className = '',
  session,
  onSignOut
}) => {
  const { settings, toggleSidePanel } = useAppSettings();
  const isCollapsed = settings.sidePanelCollapsed;

  return (
    <aside className={`h-full border-r border-white/10 bg-black/30 backdrop-blur-xl hidden lg:flex flex-col flex-shrink-0 transition-all duration-300 ease-in-out shadow-2xl ${
      isCollapsed ? 'w-20' : 'w-64'
    } ${className}`}>
      {/* Header with Toggle Button - Fixed Height */}
      <div className={`flex items-center p-4 border-b border-white/10 bg-gradient-to-r from-black/20 to-black/10 backdrop-blur-sm flex-shrink-0 ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
        {isCollapsed ? (
          <button
            onClick={toggleSidePanel}
            className="relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 via-purple-500 to-cyan-500 flex items-center justify-center shadow-lg shadow-blue-500/25 border border-white/10 hover:shadow-blue-500/40 transition-all duration-200 group"
            title="Expand sidebar"
          >
            <Image
              src="/logo/logo-light.svg"
              alt="Botmani"
              width={28}
              height={28}
              className="w-7 h-7 absolute"
            />
            <div className="absolute bottom-0 right-0 w-4 h-4 bg-black/60 rounded-tl-lg rounded-br-xl flex items-center justify-center">
              <i className="fas fa-chevron-right text-[8px] text-white"></i>
            </div>
          </button>
        ) : (
          <>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 via-purple-500 to-cyan-500 flex items-center justify-center shadow-lg shadow-blue-500/25 border border-white/10">
                <Image
                  src="/logo/logo-light.svg"
                  alt="Botmani"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-black tracking-wider bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                  BOTMANI
                </span>
                <span className="text-[9px] text-gray-400 tracking-[0.2em] uppercase font-medium -mt-1">
                  AI ECOSYSTEM
                </span>
              </div>
            </div>
            <button
              onClick={toggleSidePanel}
              className="p-2 rounded-lg bg-gradient-to-r from-white/5 to-white/10 hover:from-white/10 hover:to-white/15 text-gray-400 hover:text-white transition-all duration-200 border border-white/10 backdrop-blur-sm shadow-lg hover:shadow-blue-500/20"
              title="Collapse sidebar"
            >
              <i className="fas fa-chevron-left text-sm"></i>
            </button>
          </>
        )}
      </div>

      {/* Navigation Items - Scrollable Middle Section */}
      <div className={`flex-1 overflow-hidden ${isCollapsed ? 'px-2' : 'px-6'} pt-6 bg-gradient-to-b from-transparent to-black/10 min-h-0`}>
        <div className={`h-full overflow-y-auto overflow-x-hidden ${isCollapsed ? 'pr-1 sidebar-collapsed' : 'pr-2 sidebar-scrollbar'}`}
          style={{
            scrollbarWidth: isCollapsed ? 'none' : 'thin',
            scrollbarColor: isCollapsed ? 'transparent' : 'rgba(59, 130, 246, 0.3) rgba(255, 255, 255, 0.05)',
            maxHeight: '100%'
          }}
        >
          <nav className={`${isCollapsed ? 'space-y-4 pb-6' : 'space-y-3 pb-8'}`}>
          {items.map((item, index) => (
            <button
              key={item.id}
              onClick={() => onSectionChange(item.id)}
              className={`w-full flex items-center ${
                isCollapsed ? 'justify-center px-3 py-4' : 'space-x-3 px-4 py-3'
              } rounded-xl transition-all duration-300 text-left cursor-pointer group relative backdrop-blur-sm ${
                activeSection === item.id
                  ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30 shadow-lg shadow-blue-500/10'
                  : 'text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-white/5 hover:to-white/10 hover:border hover:border-white/10 hover:shadow-lg hover:backdrop-blur-md'
              } ${
                isCollapsed ? 'mx-1' : ''
              }`}
              title={isCollapsed ? item.label : undefined}
              style={{
                animationDelay: `${index * 50}ms`,
              }}
            >
              <i className={`${item.icon} ${isCollapsed ? 'text-xl' : 'w-5 text-center'} ${
                activeSection === item.id
                  ? 'text-blue-400'
                  : 'group-hover:text-purple-400'
              } transition-colors duration-200`}></i>
              {!isCollapsed && (
                <span className="font-medium transition-all duration-200">{item.label}</span>
              )}
              
              {/* Tooltip for collapsed state */}
              {isCollapsed && (
                <div className="absolute left-full ml-4 px-3 py-2 bg-black/95 backdrop-blur-xl text-white text-sm rounded-xl border border-white/20 shadow-2xl opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-50 group-hover:translate-x-1">
                  {item.label}
                  <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-2 w-0 h-0 border-t-[6px] border-b-[6px] border-r-[6px] border-transparent border-r-black/95"></div>
                </div>
              )}
              
              {/* Active indicator for expanded state */}
              {activeSection === item.id && !isCollapsed && (
                <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-blue-400 to-purple-400 rounded-r-full shadow-lg shadow-blue-500/50"></div>
              )}
              
              {/* Active indicator for collapsed state */}
              {activeSection === item.id && isCollapsed && (
                <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-10 bg-gradient-to-b from-blue-400 to-purple-400 rounded-r-full shadow-lg shadow-blue-500/50"></div>
              )}
            </button>
          ))}
          </nav>
        </div>
      </div>

      {/* User Profile Section - Fixed at Bottom */}
      {session?.user && (
        <div className={`border-t border-white/10 ${isCollapsed ? 'p-2' : 'p-4'} bg-gradient-to-t from-black/20 to-transparent flex-shrink-0`}>
          <div className="relative group">
            <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'} ${isCollapsed ? 'p-2' : 'p-3'} rounded-xl bg-gradient-to-r from-white/5 to-white/10 hover:from-white/10 hover:to-white/15 transition-all duration-200 border border-white/10 backdrop-blur-sm cursor-pointer`}>
              {/* User Avatar */}
              <div className="flex-shrink-0">
                {session.user.image ? (
                  <div className={`${isCollapsed ? 'w-8 h-8' : 'w-10 h-10'} rounded-full border-2 border-white/20 overflow-hidden`}>
                    <Image
                      src={session.user.image}
                      alt={session.user.name || 'User'}
                      width={isCollapsed ? 32 : 40}
                      height={isCollapsed ? 32 : 40}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className={`${isCollapsed ? 'w-8 h-8 text-xs' : 'w-10 h-10 text-sm'} rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white font-semibold border-2 border-white/20`}>
                    {session.user.name?.charAt(0).toUpperCase() || 'U'}
                  </div>
                )}
              </div>
              
              {/* User Info (only when expanded) */}
              {!isCollapsed && (
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-white truncate">
                    {session.user.name || 'User'}
                  </div>
                  <div className="text-xs text-gray-400 truncate">
                    {session.user.email || '<EMAIL>'}
                  </div>
                </div>
              )}
              
              {/* Dropdown indicator (only when expanded) */}
              {!isCollapsed && (
                <div className="flex-shrink-0">
                  <i className="fas fa-chevron-up text-xs text-gray-400 group-hover:text-white transition-colors duration-200"></i>
                </div>
              )}
            </div>
            
            {/* Dropdown Menu */}
            <div className={`absolute ${isCollapsed ? 'left-full ml-4 bottom-0' : 'top-0 left-0 right-0 -translate-y-full'} opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none group-hover:pointer-events-auto z-50`}>
              {/* Invisible bridge to prevent dropdown from disappearing */}
              <div className={`${isCollapsed ? 'w-6 h-full' : 'w-full h-4'} bg-transparent`}></div>
              <div className={`bg-black/95 backdrop-blur-xl rounded-xl border border-white/20 shadow-2xl ${isCollapsed ? 'p-2 w-48' : 'p-2'}`}>
                {/* User info (shown in collapsed mode) */}
                {isCollapsed && (
                  <div className="p-3 border-b border-white/10">
                    <div className="text-sm font-medium text-white">{session.user.name || 'User'}</div>
                    <div className="text-xs text-gray-400">{session.user.email || '<EMAIL>'}</div>
                  </div>
                )}
                <button
                  onClick={() => {
                    if (onSignOut) {
                      onSignOut();
                      // Force redirect to home page after sign out
                      window.location.href = '/';
                    }
                  }}
                  className="w-full text-left px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-white/5 rounded-lg transition-colors duration-200 flex items-center space-x-2 cursor-pointer"
                >
                  <i className="fas fa-sign-out-alt text-xs"></i>
                  <span>Sign Out</span>
                </button>
              </div>
              
              {/* Tooltip arrow for collapsed state */}
              {isCollapsed && (
                <div className="absolute left-0 top-4 -translate-x-2 w-0 h-0 border-t-[6px] border-b-[6px] border-r-[6px] border-transparent border-r-black/95"></div>
              )}
            </div>
            
            {/* Tooltip for collapsed state (when not hovering dropdown) */}
            {isCollapsed && (
              <div className="absolute left-full ml-4 top-1/2 -translate-y-1/2 px-3 py-2 bg-black/95 backdrop-blur-xl text-white text-sm rounded-xl border border-white/20 shadow-2xl opacity-0 hover:!opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-40 group-hover:translate-x-1">
                {session.user.name || 'User'}
                <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-2 w-0 h-0 border-t-[6px] border-b-[6px] border-r-[6px] border-transparent border-r-black/95"></div>
              </div>
            )}
          </div>
        </div>
      )}
    </aside>
  );
};

export default SidePanel;