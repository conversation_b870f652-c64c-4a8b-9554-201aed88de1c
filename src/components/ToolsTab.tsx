import React, { useState } from 'react';

interface Tool {
  type: string;
  name: string;
  icon: string;
  description: string;
  category: 'Development' | 'Integration' | 'Data' | 'Search' | 'Communication';
}

const ToolsTab: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const tools: Tool[] = [
    {
      type: 'FUNCTION',
      name: 'Function',
      icon: 'fas fa-code',
      description: 'Custom function execution for complex logic and calculations',
      category: 'Development'
    },
    {
      type: 'API',
      name: 'API',
      icon: 'fas fa-plug',
      description: 'Connect to external APIs and services for data integration',
      category: 'Integration'
    },
    {
      type: 'DATABASE',
      name: 'Database',
      icon: 'fas fa-database',
      description: 'Query and manage database connections and operations',
      category: 'Data'
    },
    {
      type: 'WEB_SCRAPER',
      name: 'Web Scraper',
      icon: 'fas fa-spider',
      description: 'Extract data from websites and web pages automatically',
      category: 'Data'
    },
    {
      type: 'FILE_PROCESSOR',
      name: 'File Processor',
      icon: 'fas fa-file-alt',
      description: 'Process, transform, and manipulate various file formats',
      category: 'Data'
    },
    {
      type: 'CALCULATOR',
      name: 'Calculator',
      icon: 'fas fa-calculator',
      description: 'Perform mathematical calculations and computations',
      category: 'Development'
    },
    {
      type: 'CODE_EXECUTOR',
      name: 'Code Executor',
      icon: 'fas fa-terminal',
      description: 'Execute code snippets in various programming languages',
      category: 'Development'
    },
    {
      type: 'APIFY',
      name: 'Apify',
      icon: 'fas fa-robot',
      description: 'Web scraping and automation platform integration',
      category: 'Integration'
    },
    {
      type: 'BRAVE_SEARCH',
      name: 'Brave Search',
      icon: 'fas fa-search',
      description: 'Privacy-focused search engine for web queries',
      category: 'Search'
    },
    {
      type: 'FIRECRAWL',
      name: 'Firecrawl',
      icon: 'fas fa-fire',
      description: 'Advanced web crawling and data extraction service',
      category: 'Data'
    },
    {
      type: 'GITHUB',
      name: 'GitHub',
      icon: 'fab fa-github',
      description: 'Version control and code repository management',
      category: 'Development'
    },
    {
      type: 'GMAIL',
      name: 'Gmail',
      icon: 'fas fa-envelope',
      description: 'Email management and automation through Gmail API',
      category: 'Communication'
    },
    {
      type: 'GOOGLE_MAPS',
      name: 'Google Maps',
      icon: 'fas fa-map-marker-alt',
      description: 'Location services and mapping functionality',
      category: 'Integration'
    }
  ];

  const categories = ['All', 'Development', 'Integration', 'Data', 'Search', 'Communication'];

  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || tool.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Development': return 'from-blue-500 to-cyan-500';
      case 'Integration': return 'from-purple-500 to-pink-500';
      case 'Data': return 'from-green-500 to-emerald-500';
      case 'Search': return 'from-orange-500 to-red-500';
      case 'Communication': return 'from-indigo-500 to-purple-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div className="min-h-full">
      {/* Header Section */}
      <div className="border-b border-white/10 bg-black/20 backdrop-blur-xl sticky top-0 z-10">
        <div className="p-8">
          <div className="max-w-6xl mx-auto">
            <h1 className="text-4xl font-bold mb-2 text-white">Tools</h1>
            <p className="text-gray-400 mb-8">Discover and manage AI tools for your agents</p>
            
            {/* Search Bar */}
            <div className="relative mb-6">
              <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                <svg className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search tools..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-16 pr-6 py-4 text-lg bg-white/5 border-2 border-white/10 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-400/50 focus:bg-white/10 transition-all duration-300 backdrop-blur-lg"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                    selectedCategory === category
                      ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30'
                      : 'bg-white/5 text-gray-400 hover:text-white hover:bg-white/10 border border-white/10'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-8 pb-32">
        <div className="max-w-6xl mx-auto">
          
          {/* Tools Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredTools.map((tool, index) => (
              <div
                key={tool.type}
                className="glass-card rounded-2xl p-6 border border-white/10 hover:bg-white/5 transition-all duration-300 group cursor-pointer"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                {/* Content */}
                <div className="flex flex-col h-full">
                  {/* Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-br ${getCategoryColor(tool.category)} rounded-full flex items-center justify-center mb-4 group-hover:scale-105 transition-transform duration-300 shadow-lg`}>
                    <i className={`${tool.icon} text-2xl text-white`}></i>
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300">
                    {tool.name}
                  </h3>

                  {/* Category Badge */}
                  <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mb-3 bg-gradient-to-r ${getCategoryColor(tool.category)} text-white opacity-80 self-start`}>
                    {tool.category}
                  </div>

                  {/* Description */}
                  <p className="text-gray-300 text-sm leading-relaxed group-hover:text-gray-200 transition-colors duration-300 flex-1">
                    {tool.description}
                  </p>

                  {/* Add Button */}
                  <div className="mt-4 pt-4 border-t border-white/10">
                    <button className="w-full px-4 py-2 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-xl text-white text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2">
                      <i className="fas fa-plus text-xs"></i>
                      <span>Add Tool</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Empty State */}
          {filteredTools.length === 0 && (
            <div className="text-center py-16">
              <div className="w-16 h-16 rounded-full bg-gray-700/50 flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-tools text-gray-400 text-xl"></i>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">No tools found</h3>
              <p className="text-gray-400 mb-6">
                {searchQuery || selectedCategory !== 'All' 
                  ? 'Try adjusting your search or filter criteria' 
                  : 'No tools available at the moment'}
              </p>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('All');
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl transition-all duration-200"
              >
                Clear Filters
              </button>
            </div>
          )}

        </div>
      </div>
    </div>
  );
};

export default ToolsTab;