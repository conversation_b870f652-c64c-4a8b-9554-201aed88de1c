import React, { useState } from 'react';

interface UsageMetrics {
  period: string;
  requests: number;
  tokens: number;
  cost: number;
  limit: number;
}

interface PlanLimits {
  name: string;
  requests: number;
  tokens: number;
  cost: number;
  agents: number;
  workflows: number;
}

const UsageLimitationTab: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month'>('month');
  
  const usageData: Record<string, UsageMetrics> = {
    day: {
      period: 'Today',
      requests: 245,
      tokens: 12500,
      cost: 3.75,
      limit: 1000
    },
    week: {
      period: 'This Week',
      requests: 1580,
      tokens: 89200,
      cost: 26.76,
      limit: 5000
    },
    month: {
      period: 'This Month',
      requests: 6850,
      tokens: 345000,
      cost: 103.50,
      limit: 10000
    }
  };

  const currentPlan: PlanLimits = {
    name: 'Professional',
    requests: 10000,
    tokens: 500000,
    cost: 100.00,
    agents: 25,
    workflows: 50
  };

  const currentUsage = usageData[selectedPeriod];
  const usagePercentage = (currentUsage.requests / currentUsage.limit) * 100;

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-400 bg-red-400/10';
    if (percentage >= 75) return 'text-yellow-400 bg-yellow-400/10';
    return 'text-green-400 bg-green-400/10';
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="min-h-full">
      {/* Content Header */}
      <div className="border-b border-white/10 bg-black/20 backdrop-blur-xl sticky top-0 z-10">
        <div className="p-8">
          <div className="max-w-4xl">
            <h1 className="text-4xl font-bold mb-2">Usage & Limitations</h1>
            <p className="text-gray-400">Monitor your usage and manage plan limits</p>
            
            {/* Period Selection */}
            <div className="flex space-x-1 mt-6">
              {(['day', 'week', 'month'] as const).map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`px-6 py-2 rounded-full transition-all duration-200 text-sm font-medium capitalize ${
                    selectedPeriod === period
                      ? 'bg-white/10 text-white border border-white/20'
                      : 'text-gray-400 hover:text-white hover:bg-white/5'
                  }`}
                >
                  {period}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-8 pb-32">
        <div className="max-w-4xl">
          {/* Current Usage Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="glass-card rounded-2xl p-6 border border-white/10">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center">
                    <i className="fas fa-bolt text-blue-400"></i>
                  </div>
                  <h3 className="text-white font-semibold">API Requests</h3>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUsageColor(usagePercentage)}`}>
                  {usagePercentage.toFixed(1)}%
                </span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-2xl font-bold text-white">{currentUsage.requests.toLocaleString()}</span>
                  <span className="text-gray-400 text-sm">of {currentUsage.limit.toLocaleString()}</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(usagePercentage)}`}
                    style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="glass-card rounded-2xl p-6 border border-white/10">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 rounded-lg bg-purple-500/20 flex items-center justify-center">
                  <i className="fas fa-coins text-purple-400"></i>
                </div>
                <h3 className="text-white font-semibold">Tokens Used</h3>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-2xl font-bold text-white">{currentUsage.tokens.toLocaleString()}</span>
                  <span className="text-gray-400 text-sm">tokens</span>
                </div>
                <p className="text-gray-400 text-sm">{currentUsage.period}</p>
              </div>
            </div>

            <div className="glass-card rounded-2xl p-6 border border-white/10">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 rounded-lg bg-green-500/20 flex items-center justify-center">
                  <i className="fas fa-dollar-sign text-green-400"></i>
                </div>
                <h3 className="text-white font-semibold">Cost</h3>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-2xl font-bold text-white">${currentUsage.cost.toFixed(2)}</span>
                  <span className="text-gray-400 text-sm">{currentUsage.period}</span>
                </div>
                <p className="text-gray-400 text-sm">Average: ${(currentUsage.cost / currentUsage.requests * 1000).toFixed(3)}/1K requests</p>
              </div>
            </div>
          </div>

          {/* Current Plan */}
          <div className="glass-card rounded-2xl p-6 border border-white/10 mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-semibold text-white mb-2">Current Plan: {currentPlan.name}</h2>
                <p className="text-gray-400">Your current subscription and limits</p>
              </div>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl transition-all duration-200">
                Upgrade Plan
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="bg-white/5 rounded-xl p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <i className="fas fa-bolt text-blue-400 text-sm"></i>
                  <span className="text-white font-medium">Requests</span>
                </div>
                <p className="text-2xl font-bold text-white">{currentPlan.requests.toLocaleString()}</p>
                <p className="text-gray-400 text-sm">per month</p>
              </div>

              <div className="bg-white/5 rounded-xl p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <i className="fas fa-coins text-purple-400 text-sm"></i>
                  <span className="text-white font-medium">Tokens</span>
                </div>
                <p className="text-2xl font-bold text-white">{currentPlan.tokens.toLocaleString()}</p>
                <p className="text-gray-400 text-sm">per month</p>
              </div>

              <div className="bg-white/5 rounded-xl p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <i className="fas fa-robot text-green-400 text-sm"></i>
                  <span className="text-white font-medium">Agents</span>
                </div>
                <p className="text-2xl font-bold text-white">{currentPlan.agents}</p>
                <p className="text-gray-400 text-sm">maximum</p>
              </div>

              <div className="bg-white/5 rounded-xl p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <i className="fas fa-project-diagram text-yellow-400 text-sm"></i>
                  <span className="text-white font-medium">Workflows</span>
                </div>
                <p className="text-2xl font-bold text-white">{currentPlan.workflows}</p>
                <p className="text-gray-400 text-sm">maximum</p>
              </div>

              <div className="bg-white/5 rounded-xl p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <i className="fas fa-dollar-sign text-red-400 text-sm"></i>
                  <span className="text-white font-medium">Budget</span>
                </div>
                <p className="text-2xl font-bold text-white">${currentPlan.cost.toFixed(0)}</p>
                <p className="text-gray-400 text-sm">per month</p>
              </div>
            </div>
          </div>

          {/* Usage Warnings */}
          {usagePercentage >= 75 && (
            <div className={`glass-card rounded-2xl p-6 border border-white/10 mb-8 ${
              usagePercentage >= 90 ? 'border-red-500/50 bg-red-500/5' : 'border-yellow-500/50 bg-yellow-500/5'
            }`}>
              <div className="flex items-center space-x-3 mb-4">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                  usagePercentage >= 90 ? 'bg-red-500/20' : 'bg-yellow-500/20'
                }`}>
                  <i className={`fas fa-exclamation-triangle ${
                    usagePercentage >= 90 ? 'text-red-400' : 'text-yellow-400'
                  }`}></i>
                </div>
                <div>
                  <h3 className={`font-semibold ${
                    usagePercentage >= 90 ? 'text-red-400' : 'text-yellow-400'
                  }`}>
                    {usagePercentage >= 90 ? 'Usage Limit Warning' : 'High Usage Alert'}
                  </h3>
                  <p className="text-gray-400 text-sm">
                    {usagePercentage >= 90 
                      ? 'You are approaching your monthly request limit. Consider upgrading your plan.'
                      : 'You have used more than 75% of your monthly request limit.'
                    }
                  </p>
                </div>
              </div>
              <div className="flex space-x-3">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 text-sm">
                  Upgrade Plan
                </button>
                <button className="text-gray-400 hover:text-white hover:bg-white/10 px-4 py-2 rounded-lg transition-all duration-200 text-sm">
                  View Details
                </button>
              </div>
            </div>
          )}

          {/* Usage History Chart Placeholder */}
          <div className="glass-card rounded-2xl p-8 border border-white/10 text-center">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-chart-line text-white text-xl"></i>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Usage Analytics</h3>
            <p className="text-gray-400 mb-4">Detailed usage charts and analytics coming soon</p>
            <button className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all duration-200">
              Enable Analytics
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UsageLimitationTab; 