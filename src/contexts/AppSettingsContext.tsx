import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface AppSettings {
  sidePanelCollapsed: boolean;
}

interface AppSettingsContextType {
  settings: AppSettings;
  updateSettings: (updates: Partial<AppSettings>) => void;
  toggleSidePanel: () => void;
}

const defaultSettings: AppSettings = {
  sidePanelCollapsed: false,
};

const AppSettingsContext = createContext<AppSettingsContextType | undefined>(undefined);

interface AppSettingsProviderProps {
  children: ReactNode;
}

export const AppSettingsProvider: React.FC<AppSettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<AppSettings>(defaultSettings);

  // Load settings from localStorage on mount
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem('app-settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        setSettings(prevSettings => ({ ...prevSettings, ...parsed }));
      }
    } catch (error) {
      console.warn('Failed to load app settings from localStorage:', error);
    }
  }, []);

  // Save settings to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem('app-settings', JSON.stringify(settings));
    } catch (error) {
      console.warn('Failed to save app settings to localStorage:', error);
    }
  }, [settings]);

  const updateSettings = (updates: Partial<AppSettings>) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      ...updates,
    }));
  };

  const toggleSidePanel = () => {
    setSettings(prevSettings => ({
      ...prevSettings,
      sidePanelCollapsed: !prevSettings.sidePanelCollapsed,
    }));
  };

  const contextValue: AppSettingsContextType = {
    settings,
    updateSettings,
    toggleSidePanel,
  };

  return (
    <AppSettingsContext.Provider value={contextValue}>
      {children}
    </AppSettingsContext.Provider>
  );
};

export const useAppSettings = (): AppSettingsContextType => {
  const context = useContext(AppSettingsContext);
  if (context === undefined) {
    throw new Error('useAppSettings must be used within an AppSettingsProvider');
  }
  return context;
};

export default AppSettingsContext;