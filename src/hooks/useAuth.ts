import { useState, useEffect } from 'react'
import Cookies from 'js-cookie'
import type { User, Tokens, Session, AuthStatus, UseAuthReturn } from '../types/auth'

export function useAuth(): UseAuthReturn {
  const [session, setSession] = useState<Session | null>(null)
  const [status, setStatus] = useState<AuthStatus>('loading')

  const clearAllCookies = () => {
    Cookies.remove('botmani-session')
    Cookies.remove('botmani-access-token') // JWT token
    Cookies.remove('botmani-google-access-token') // Google token
    Cookies.remove('botmani-id-token')
    Cookies.remove('botmani-refresh-token')
  }

  useEffect(() => {
    // Check for existing session
    const checkSession = () => {
      try {
        const storedSession = Cookies.get('botmani-session')
        if (storedSession) {
          const parsedSession = JSON.parse(storedSession)
          const now = new Date()
          const expires = new Date(parsedSession.expires)
          
          if (expires > now) {
            setSession(parsedSession)
            setStatus('authenticated')
          } else {
            // Session expired - clear all cookies
            clearAllCookies()
            setSession(null)
            setStatus('unauthenticated')
          }
        } else {
          setSession(null)
          setStatus('unauthenticated')
        }
      } catch (error) {
        console.error('Error checking session:', error)
        clearAllCookies()
        setSession(null)
        setStatus('unauthenticated')
      }
    }

    checkSession()

    // Listen for session changes
    const handleSessionChange = (event: CustomEvent) => {
      if (event.detail) {
        setSession(event.detail)
        setStatus('authenticated')
      } else {
        clearAllCookies()
        setSession(null)
        setStatus('unauthenticated')
      }
    }

    window.addEventListener('sessionChanged', handleSessionChange as EventListener)

    return () => {
      window.removeEventListener('sessionChanged', handleSessionChange as EventListener)
    }
  }, []) // Remove clearAllCookies from dependencies as it doesn't depend on state/props

  const signOut = () => {
    clearAllCookies()
    setSession(null)
    setStatus('unauthenticated')
    window.dispatchEvent(new CustomEvent('sessionChanged', { detail: null }))
  }

  // Token utility functions - get directly from cookies for freshest data
  const getAccessToken = () => {
    // Return our JWT token for API calls (main auth token)
    return Cookies.get('botmani-access-token') || session?.tokens?.jwt_access_token || null
  }

  const getGoogleAccessToken = () => {
    // Return Google's access token for Google API calls
    return Cookies.get('botmani-google-access-token') || session?.tokens?.access_token || null
  }

  const getIdToken = () => {
    return Cookies.get('botmani-id-token') || session?.tokens?.id_token || null
  }

  const getRefreshToken = () => {
    return Cookies.get('botmani-refresh-token') || session?.tokens?.refresh_token || null
  }

  const isTokenExpired = () => {
    if (!session?.tokens?.expires_at) return true
    return new Date() > new Date(session.tokens.expires_at)
  }

  return {
    session,
    status,
    signOut,
    getAccessToken,
    getGoogleAccessToken,
    getIdToken,
    getRefreshToken,
    isTokenExpired,
  }
} 