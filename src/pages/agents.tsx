import Image from "next/image";
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import { useState, useEffect } from "react";
import Head from "next/head";
import { useAuth } from "../hooks/useAuth";
import type { Session } from "../types/auth";
import Layout from "../components/Layout";
import MyAgentsTab from "../components/MyAgentsTab";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function Agents() {
  const [isLoaded, setIsLoaded] = useState(false);
  const { session, status, signOut } = useAuth();

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const handleGoogleSignIn = () => {
    const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?${new URLSearchParams({
      client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
      redirect_uri: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI || '',
      response_type: 'code',
      scope: 'openid email profile',
      access_type: 'offline',
      prompt: 'consent',
    }).toString()}`;
    
    window.location.href = googleAuthUrl;
  };

  const handleSignOut = () => {
    signOut();
  };

  // If user is authenticated, show agents page with layout
  if (session) {
    return (
      <Layout title="Agents - Botmani">
        <MyAgentsTab />
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Agents - Botmani</title>
        <meta name="description" content="Build and manage your AI agents with Botmani" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
      </Head>
      <div className={`${geistSans.variable} ${geistMono.variable} min-h-screen bg-black font-sans overflow-hidden`}>
        {status === 'loading' && (
          <div className="fixed inset-0 z-50 bg-black flex items-center justify-center">
            <div className="flex items-center space-x-4 text-white">
              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span className="text-lg">Loading...</span>
            </div>
          </div>
        )}

        {/* Background Effects */}
        <div className="fixed inset-0 bg-gradient-to-br from-blue-900/10 via-purple-900/10 to-black"></div>
        <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/15 via-transparent to-transparent"></div>
        
        {/* Floating Elements */}
        <div className="fixed top-1/4 left-1/4 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl animate-float"></div>
        <div className="fixed top-3/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
        
        {/* Header */}
        <header className="relative z-10 p-6 sm:p-8">
          <nav className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 via-purple-500 to-cyan-500 flex items-center justify-center shadow-lg shadow-blue-500/25 border border-white/10">
                <Image
                  src="/logo/logo-light.svg"
                  alt="Botmani"
                  width={28}
                  height={28}
                  className="w-7 h-7"
                />
              </div>
              <div className="flex flex-col">
                <span className="text-2xl font-black tracking-wider bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                  BOTMANI
                </span>
                <span className="text-[10px] text-gray-400 tracking-[0.2em] uppercase font-medium -mt-1">
                  AI ECOSYSTEM
                </span>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-6">
              <a href="/" className="text-gray-300 hover:text-white transition-colors">Home</a>
              <a href="/agents" className="text-white font-medium border-b-2 border-blue-400">Agents</a>
              <a href="/workflows" className="text-gray-300 hover:text-white transition-colors">Workflows</a>
              <a href="/ecosystem" className="text-gray-300 hover:text-white transition-colors">Ecosystem</a>
              <button 
                onClick={handleGoogleSignIn}
                className="glass-card px-6 py-2 rounded-full text-white hover:bg-white/10 transition-all duration-300 flex items-center space-x-2 cursor-pointer"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                </svg>
                <span>Sign In</span>
              </button>
            </div>
          </nav>
        </header>

        {/* Hero Section */}
        <main className="relative z-10 px-6 sm:px-8 pt-20 pb-32">
          <div className="max-w-7xl mx-auto relative z-20">
            <div className="text-center max-w-5xl mx-auto">
              <div className={`transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
                <h1 className="text-5xl sm:text-7xl font-black mb-8">
                  <span className="block text-white mb-4">Build Intelligent</span>
                  <span className="block bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                    AI Agents
                  </span>
                </h1>
                <p className="text-xl sm:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
                  Create sophisticated AI agents that understand context, collaborate seamlessly, and execute complex workflows across multiple channels and platforms.
                </p>
                
                {/* Agent Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                  <div className="glass-card rounded-2xl p-8 border border-white/10">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                      <i className="fas fa-brain text-2xl text-white"></i>
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">Multi-Model AI</h3>
                    <p className="text-gray-300 leading-relaxed">
                      Choose from OpenAI GPT-4, Anthropic Claude, Google Gemini, Groq, and HuggingFace models for each agent's unique needs.
                    </p>
                  </div>
                  
                  <div className="glass-card rounded-2xl p-8 border border-white/10">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                      <i className="fas fa-comments text-2xl text-white"></i>
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">Context Sharing</h3>
                    <p className="text-gray-300 leading-relaxed">
                      Agents seamlessly share context and collaborate, building upon each other's work for complex problem solving.
                    </p>
                  </div>
                  
                  <div className="glass-card rounded-2xl p-8 border border-white/10">
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                      <i className="fas fa-network-wired text-2xl text-white"></i>
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">Channel Deploy</h3>
                    <p className="text-gray-300 leading-relaxed">
                      Deploy agents instantly to WhatsApp, Slack, Telegram, websites, and 15+ other communication channels.
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                  <button
                    onClick={handleGoogleSignIn}
                    className="glass-card px-8 py-4 rounded-full text-white hover:bg-white/10 transition-all duration-300 flex items-center space-x-3 cursor-pointer text-lg font-medium"
                  >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                      <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                      <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                      <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                    </svg>
                    <span>Start Building Agents</span>
                  </button>
                  <button className="px-8 py-4 rounded-full border border-white/20 text-white hover:bg-white/5 transition-all duration-300 flex items-center space-x-3 cursor-pointer text-lg font-medium">
                    <i className="fas fa-play"></i>
                    <span>Watch Demo</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="relative z-10 px-6 sm:px-8 py-12 border-t border-white/10">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center space-x-4 mb-4 md:mb-0">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 via-purple-500 to-cyan-500 flex items-center justify-center shadow-lg shadow-blue-500/25 border border-white/10">
                  <Image
                    src="/logo/logo-light.svg"
                    alt="Botmani"
                    width={24}
                    height={24}
                    className="w-6 h-6"
                  />
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-black tracking-wider bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                    BOTMANI
                  </span>
                  <span className="text-[8px] text-gray-400 tracking-[0.2em] uppercase font-medium -mt-1">
                    AI ECOSYSTEM
                  </span>
                </div>
              </div>
              <div className="flex items-center space-x-6">
                <a href="#privacy" className="text-gray-400 hover:text-white transition-colors">Privacy</a>
                <a href="#terms" className="text-gray-400 hover:text-white transition-colors">Terms</a>
                <a href="#contact" className="text-gray-400 hover:text-white transition-colors">Contact</a>
              </div>
            </div>
            <div className="mt-8 pt-8 border-t border-white/10 text-center text-gray-400">
              <p>&copy; 2025 Botmani. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}