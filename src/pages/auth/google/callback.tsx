import { GetServerSideProps } from 'next'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import Image from 'next/image'
import { <PERSON>ei<PERSON> } from 'next/font/google'
import Cookies from 'js-cookie'

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
})

interface CallbackProps {
  success: boolean
  error?: string
  user?: any
  tokens?: {
    access_token: string
    id_token?: string
    refresh_token?: string
    expires_in: number
    token_type: string
    expires_at: string
    jwt_access_token?: string
    provider?: string
  }
  isClientSide?: boolean
  code?: string
}

export default function GoogleCallbackPage({ success, error, user, tokens, isClientSide, code }: CallbackProps) {
  const router = useRouter()
  const [status, setStatus] = useState('loading')
  const [clientError, setClientError] = useState<string | null>(null)
  const [clientUser, setClientUser] = useState<any>(null)
  const [clientTokens, setClientTokens] = useState<any>(null)

  // Handle client-side authentication (development only)
  useEffect(() => {
    if (isClientSide && code) {
      handleClientSideAuth(code)
    }
  }, [isClientSide, code])

  const handleClientSideAuth = async (authCode: string) => {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1'
      const backendUrl = `${baseUrl}/auth/google/callback/token`
      
      console.log('🌐 [CLIENT] Making request to backend:', backendUrl)
      console.log('🌐 [CLIENT] Request payload:', { code: authCode })
      
      const tokenResponse = await fetch(backendUrl, {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: authCode,
        }),
        signal: AbortSignal.timeout(30000), // 30 second timeout
      })

      console.log('🌐 [CLIENT] Backend response status:', tokenResponse.status)
      
      let responseData
      try {
        responseData = await tokenResponse.json()
        console.log('🌐 [CLIENT] Backend response data:', JSON.stringify(responseData, null, 2))
      } catch (parseError) {
        console.log('🌐 [CLIENT] Failed to parse response as JSON:', parseError)
        const errorText = await tokenResponse.text()
        console.log('🌐 [CLIENT] Backend error response (text):', errorText)
        setClientError(`HTTP ${tokenResponse.status}: ${errorText}`)
        return
      }

      // Handle HTTP error status codes
      if (!tokenResponse.ok) {
        const errorMessage = responseData.error?.message || responseData.message || `HTTP ${tokenResponse.status} error`
        console.log('🌐 [CLIENT] Backend HTTP error:', errorMessage)
        setClientError(errorMessage)
        return
      }

      // Check if the response indicates success
      if (responseData.success === false) {
        const errorMessage = responseData.error?.message || responseData.message || 'Authentication failed'
        console.log('🌐 [CLIENT] Backend returned success: false:', errorMessage)
        setClientError(errorMessage)
        return
      }

      // Extract data from BaseResponse structure
      const dataPayload = responseData.data
      if (!dataPayload) {
        console.log('🌐 [CLIENT] No data payload received from backend')
        setClientError('No data received from backend')
        return
      }

      // Extract user data
      const userData = dataPayload.user
      if (!userData) {
        console.log('🌐 [CLIENT] No user data received from backend')
        setClientError('No user data received from backend')
        return
      }

      // Calculate expires_at from expires_in
      const expiresIn = dataPayload.expires_in || 3600
      const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString()

      const processedUser = {
        id: userData.id || null,
        email: userData.email || null,
        name: userData.name || userData.full_name || null,
        picture: userData.picture || userData.image || userData.avatar_url || null,
      }

      const processedTokens = {
        // Google tokens
        access_token: dataPayload.access_token || null,
        id_token: dataPayload.id_token || null,
        refresh_token: dataPayload.refresh_token || null,
        expires_in: expiresIn,
        token_type: dataPayload.token_type || 'Bearer',
        expires_at: expiresAt,
        // Our JWT token for API calls
        jwt_access_token: dataPayload.jwt_access_token || null,
        provider: dataPayload.provider || 'google',
      }

      console.log('🌐 [CLIENT] Authentication successful for user:', processedUser.email)
      setClientUser(processedUser)
      setClientTokens(processedTokens)
      
    } catch (error) {
      console.error('🌐 [CLIENT] OAuth callback error details:', error)
      
      if (error instanceof Error) {
        // Handle different types of errors more gracefully
        let errorMessage = error.message
        
        // Clean up error message for better user experience
        if (errorMessage.includes('Failed to exchange authorization code for tokens')) {
          errorMessage = 'OAuth authorization failed. Please try signing in again.'
        } else if (errorMessage.includes('fetch')) {
          errorMessage = 'Network error: Unable to connect to authentication server'
        } else if (errorMessage.includes('timeout')) {
          errorMessage = 'Authentication request timed out. Please try again.'
        } else if (errorMessage.includes('HTTP 400')) {
          errorMessage = 'Invalid authentication request. Please try signing in again.'
        } else if (errorMessage.includes('HTTP 401')) {
          errorMessage = 'Authentication failed. Please check your credentials.'
        } else if (errorMessage.includes('HTTP 403')) {
          errorMessage = 'Access forbidden. Please contact support.'
        } else if (errorMessage.includes('HTTP 500')) {
          errorMessage = 'Server error. Please try again later.'
        }
        
        console.log('🌐 [CLIENT] Processed error message:', errorMessage)
        setClientError(errorMessage)
      } else {
        setClientError('Authentication error: Unknown error occurred')
      }
    }
  }

  useEffect(() => {
    // Use client-side data if available, otherwise use server-side data
    const finalSuccess = isClientSide ? (clientUser && clientTokens && !clientError) : success
    const finalError = isClientSide ? clientError : error
    const finalUser = isClientSide ? clientUser : user
    const finalTokens = isClientSide ? clientTokens : tokens

    if (finalSuccess && finalUser) {
      // Store user session and tokens in secure cookies
      const userSession = {
        user: {
          id: finalUser.id,
          email: finalUser.email,
          name: finalUser.name,
          image: finalUser.picture,
        },
        tokens: finalTokens || null,
        expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      }
      
      // Set cookies with security options
      Cookies.set('botmani-session', JSON.stringify(userSession), {
        expires: 30, // 30 days
        secure: process.env.NODE_ENV === 'production', // HTTPS only in production
        sameSite: 'strict', // CSRF protection
      })
      
      // Store tokens separately for easy access
      if (finalTokens) {
        // Store Google access token
        if (finalTokens.access_token) {
          Cookies.set('botmani-google-access-token', finalTokens.access_token, {
            expires: new Date(finalTokens.expires_at),
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
          })
        }
        
        // Store our JWT token for API calls (this is the main auth token)
        if (finalTokens.jwt_access_token) {
          Cookies.set('botmani-access-token', finalTokens.jwt_access_token, {
            expires: 30, // 30 days (or based on JWT expiration)
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
          })
        }
        
        // Store Google ID token
        if (finalTokens.id_token) {
          Cookies.set('botmani-id-token', finalTokens.id_token, {
            expires: 7, // 7 days
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
          })
        }
        
        // Store Google refresh token
        if (finalTokens.refresh_token) {
          Cookies.set('botmani-refresh-token', finalTokens.refresh_token, {
            expires: 30, // 30 days for refresh token
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
          })
        }
      }
      
      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('sessionChanged', { detail: userSession }))
      
      setStatus('success')
      setTimeout(() => {
        router.push('/')
      }, 2000)
    } else if (finalError) {
      setStatus('error')
      setTimeout(() => {
        router.push('/')
      }, 3000)
    }
  }, [success, error, user, tokens, router, clientUser, clientTokens, clientError, isClientSide])

  return (
    <div className={`${geistSans.variable} min-h-screen bg-black font-sans flex items-center justify-center`}>
      {/* Background Effects */}
      <div className="fixed inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-black"></div>
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-900/30 via-transparent to-transparent"></div>
      
      {/* Floating Elements */}
      <div className="fixed top-1/4 left-1/4 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-float"></div>
      <div className="fixed top-3/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      
      {/* Content */}
      <div className="relative z-10 flex flex-col items-center justify-center space-y-8">
        <div className="glass-card p-8 rounded-2xl flex flex-col items-center space-y-6 max-w-md">
          <div className="flex items-center space-x-3">
            <Image
              src="/logo/logo-light.svg"
              alt="Botmani"
              width={40}
              height={40}
              className="w-10 h-10"
            />
            <span className="text-2xl font-bold text-white">Botmani</span>
          </div>
          
          {/* Status Content */}
          {status === 'loading' && (
            <>
              {/* Google Logo */}
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                </svg>
              </div>
              
              <div className="flex flex-col items-center space-y-4">
                <div className="flex space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce"></div>
                  <div className="w-3 h-3 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                
                <div className="text-center">
                  <h2 className="text-xl font-semibold text-white mb-2">Connecting to Google</h2>
                  <p className="text-gray-400 text-sm">Please wait while we authenticate your account...</p>
                </div>
              </div>
            </>
          )}

          {status === 'success' && (
            <>
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 16.2L5.8 13l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" fill="white"/>
                </svg>
              </div>
              
              <div className="text-center">
                <h2 className="text-xl font-semibold text-white mb-2">Welcome {(isClientSide ? clientUser?.name : user?.name)?.split(' ')[0]}!</h2>
                <p className="text-gray-400 text-sm">Authentication successful. Redirecting to dashboard...</p>
                {isClientSide && <p className="text-blue-400 text-xs mt-1">🌐 Client-side mode (dev)</p>}
              </div>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="white"/>
                </svg>
              </div>
              
              <div className="text-center">
                <h2 className="text-xl font-semibold text-white mb-2">Authentication Failed</h2>
                <p className="text-gray-400 text-sm">Something went wrong. Redirecting to home page...</p>
                {(isClientSide ? clientError : error) && <p className="text-red-400 text-xs mt-2">{isClientSide ? clientError : error}</p>}
                {isClientSide && <p className="text-blue-400 text-xs mt-1">🌐 Client-side mode (dev)</p>}
              </div>
            </>
          )}
        </div>
        
        {/* Animated Background Elements */}
        <div className="absolute -top-20 -left-20 w-40 h-40 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute -bottom-20 -right-20 w-32 h-32 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>
    </div>
  )
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { code, error } = context.query
  const isProduction = process.env.NEXT_PUBLIC_STAGE === 'prod'

  if (error) {
    return {
      props: {
        success: false,
        error: error as string,
        isClientSide: !isProduction,
      },
    }
  }

  if (!code) {
    return {
      props: {
        success: false,
        error: 'No authorization code received',
        isClientSide: !isProduction,
      },
    }
  }

  // In development, pass the code to client-side for visible network requests
  if (!isProduction) {
    console.log('🔧 [DEV] Using client-side authentication for Network tab visibility')
    return {
      props: {
        success: false, // Will be handled client-side
        isClientSide: true,
        code: code as string,
      },
    }
  }

  // Production: Use secure server-side implementation
  console.log('🔒 [PROD] Using secure server-side authentication')
  
  // Use the secure backend token endpoint
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1'
  const backendUrl = `${baseUrl}/auth/google/callback/token`

  try {
    
    console.log('Making request to backend:', backendUrl)
    console.log('Request payload:', { code: code as string })
    
    const tokenResponse = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code: code as string,
      }),
      signal: AbortSignal.timeout(30000), // 30 second timeout
    })

    console.log('Backend response status:', tokenResponse.status)
    
    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text()
      console.log('Backend error response:', errorText)
      throw new Error(`HTTP ${tokenResponse.status}: ${errorText}`)
    }

    const responseData = await tokenResponse.json()
    console.log('Backend response data:', JSON.stringify(responseData, null, 2))

    // Check if the response indicates success
    if (responseData.success === false) {
      console.log('Backend returned success: false')
      return {
        props: {
          success: false,
          error: responseData.message || 'Authentication failed',
          isClientSide: false,
        },
      }
    }

    // Extract data from BaseResponse structure
    const dataPayload = responseData.data
    if (!dataPayload) {
      console.log('No data payload received from backend')
      return {
        props: {
          success: false,
          error: 'No data received from backend',
          isClientSide: false,
        },
      }
    }

    // Extract user data
    const userData = dataPayload.user
    if (!userData) {
      console.log('No user data received from backend')
      return {
        props: {
          success: false,
          error: 'No user data received from backend',
          isClientSide: false,
        },
      }
    }

    // Calculate expires_at from expires_in
    const expiresIn = dataPayload.expires_in || 3600
    const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString()

    // Ensure all values are serializable (no undefined values)
    const safeProps = {
      success: true,
      user: {
        id: userData.id || null,
        email: userData.email || null,
        name: userData.name || userData.full_name || null,
        picture: userData.picture || userData.image || userData.avatar_url || null,
      },
      tokens: {
        // Google tokens
        access_token: dataPayload.access_token || null,
        id_token: dataPayload.id_token || null,
        refresh_token: dataPayload.refresh_token || null,
        expires_in: expiresIn,
        token_type: dataPayload.token_type || 'Bearer',
        expires_at: expiresAt,
        // Our JWT token for API calls
        jwt_access_token: dataPayload.jwt_access_token || null,
        provider: dataPayload.provider || 'google',
      },
      isClientSide: false,
    }

    console.log('Returning success with user:', safeProps.user.email)
    return {
      props: safeProps,
    }
  } catch (error) {
    console.error('OAuth callback error details:', error)
    
    // Handle fetch error response
    if (error instanceof Error) {
      console.log('Fetch error details:')
      console.log('- URL:', backendUrl)
      console.log('- Method: POST')
      console.log('- Error message:', error.message)
      
      // Check if it's a network error
      if (error.name === 'TypeError' || error.message.includes('fetch')) {
        return {
          props: {
            success: false,
            error: error.message,
            isClientSide: false,
          },
        }
      }
      
      // Check if it's an HTTP error with status
      if (error.message.includes('HTTP')) {
        const statusMatch = error.message.match(/HTTP (\d+)/)
        const status = statusMatch ? statusMatch[1] : 'unknown'
        
        return {
          props: {
            success: false,
            error: `Backend error: ${status}`,
            isClientSide: false,
          },
        }
      }
      
      return {
        props: {
          success: false,
          error: `Authentication error: ${error.message}`,
          isClientSide: false,
        },
      }
    }
    
    return {
      props: {
        success: false,
        error: 'Authentication error: Unknown error occurred',
        isClientSide: false,
      },
    }
  }
}
