import Image from "next/image";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { useState, useEffect, useRef } from "react";
import Head from "next/head";
import { useAuth } from "../hooks/useAuth";
import type { Session } from "../types/auth";
import Dashboard from "../components/Dashboard";
import Layout from "../components/Layout";
import ExploreTab from "../components/ExploreTab";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Custom hook for scroll animations
const useScrollAnimation = () => {
  const [visibleElements, setVisibleElements] = useState<Set<string>>(new Set());

  useEffect(() => {
    const observers = new Map();
    
    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setVisibleElements(prev => new Set([...prev, entry.target.id]));
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, {
      threshold: 0.1,
      rootMargin: '0px 0px -100px 0px'
    });

    // Observe all elements with scroll-animate class
    const elements = document.querySelectorAll('.scroll-animate');
    elements.forEach(element => {
      if (element.id) {
        observer.observe(element);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  return visibleElements;
};

export default function Home() {
  const [isLoaded, setIsLoaded] = useState(false);
  const visibleElements = useScrollAnimation();
  const { session, status, signOut } = useAuth();

  // Type guard to help TypeScript understand session type
  const isAuthenticated = status === 'authenticated' && session;

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const isVisible = (id: string) => visibleElements.has(id);

  const handleGoogleSignIn = () => {
    // Direct Google OAuth URL
    const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?${new URLSearchParams({
      client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
      redirect_uri: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI || '',
      response_type: 'code',
      scope: 'openid email profile', // Added 'openid' to get ID token
      access_type: 'offline', // To get refresh token
      prompt: 'consent',
    }).toString()}`;
    
    window.location.href = googleAuthUrl;
  };

  const handleSignOut = () => {
    signOut();
  };

  // If user is authenticated, show explore content (making explore the home page)
  if (session) {
    return (
      <>
        <Head>
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
        </Head>
        <Layout title="Explore - Botmani">
          <ExploreTab />
        </Layout>
      </>
    );
  }

  return (
    <>
      <Head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
      </Head>
      <div className={`${geistSans.variable} ${geistMono.variable} min-h-screen bg-black font-sans overflow-hidden`}>
        {status === 'loading' && (
          <div className="fixed inset-0 z-50 bg-black flex items-center justify-center">
            <div className="flex items-center space-x-4 text-white">
              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span className="text-lg">Loading...</span>
            </div>
          </div>
        )}
        {/* Landing page content */}
      {/* Background Effects */}
      <div className="fixed inset-0 bg-gradient-to-br from-blue-900/10 via-purple-900/10 to-black"></div>
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/15 via-transparent to-transparent"></div>
      
      {/* Floating Elements */}
      <div className="fixed top-1/4 left-1/4 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl animate-float"></div>
      <div className="fixed top-3/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      
      {/* Header */}
      <header className="relative z-10 p-6 sm:p-8">
        <nav className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 via-purple-500 to-cyan-500 flex items-center justify-center shadow-lg shadow-blue-500/25 border border-white/10">
              <Image
                src="/logo/logo-light.svg"
                alt="Botmani"
                width={28}
                height={28}
                className="w-7 h-7"
              />
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-black tracking-wider bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                BOTMANI
              </span>
              <span className="text-[10px] text-gray-400 tracking-[0.2em] uppercase font-medium -mt-1">
                AI ECOSYSTEM
              </span>
            </div>
          </div>
          <div className="hidden md:flex items-center space-x-6">
            <a href="#features" className="text-gray-300 hover:text-white transition-colors">Features</a>
            <a href="#about" className="text-gray-300 hover:text-white transition-colors">About</a>
            {status === 'loading' ? (
              <div className="glass-card px-6 py-2 rounded-full text-white">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Loading...</span>
                </div>
              </div>
            ) : isAuthenticated ? (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 glass-card px-4 py-2 rounded-full">
                  <Image
                    src={(session as Session).user.image || '/logo/logo-light.svg'}
                    alt={(session as Session).user.name || 'User'}
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                  <span className="text-white text-sm font-medium">
                    {(session as Session).user.name || 'User'}
                  </span>
                </div>
                <button 
                  onClick={handleSignOut}
                  className="glass-card px-4 py-2 rounded-full text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 cursor-pointer"
                >
                  Sign Out
                </button>
              </div>
            ) : (
              <button 
                onClick={handleGoogleSignIn}
                className="glass-card px-6 py-2 rounded-full text-white hover:bg-white/10 transition-all duration-300 flex items-center space-x-2 cursor-pointer"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                </svg>
                <span>Continue with Google</span>
              </button>
            )}
          </div>
          
          {/* Mobile Navigation */}
          <div className="md:hidden flex items-center space-x-4">
            {status === 'loading' ? (
              <div className="glass-card px-4 py-2 rounded-full text-white">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            ) : session ? (
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-2 glass-card px-3 py-2 rounded-full">
                  <Image
                    src={(session as Session).user.image || '/logo/logo-light.svg'}
                    alt={(session as Session).user.name || 'User'}
                    width={20}
                    height={20}
                    className="rounded-full"
                  />
                  <span className="text-white text-xs font-medium">
                    {(session as Session).user.name?.split(' ')[0] || 'User'}
                  </span>
                </div>
                <button 
                  onClick={handleSignOut}
                  className="glass-card px-3 py-2 rounded-full text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 text-xs cursor-pointer"
                >
                  Sign Out
                </button>
              </div>
            ) : (
              <button 
                onClick={handleGoogleSignIn}
                className="glass-card px-4 py-2 rounded-full text-white hover:bg-white/10 transition-all duration-300 flex items-center space-x-2 cursor-pointer"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                </svg>
                <span className="text-sm">Sign In</span>
              </button>
            )}
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="relative z-10 px-6 sm:px-8 pt-20 pb-32 overflow-hidden">
        {/* Enhanced Agentic Background Animation */}
        <div className="hero-background-effects">
          {/* Subtle Grid Pattern */}
          <div className="hero-grid"></div>
          
          {/* Floating Orbs */}
          <div className="floating-orb orb-1"></div>
          <div className="floating-orb orb-2"></div>
          <div className="floating-orb orb-3"></div>
          <div className="floating-orb orb-4"></div>
          
          {/* Agent Nodes with Communication */}
          <div className="agent-communication-node openai" style={{ top: '15%', left: '8%', animationDelay: '0s' }}>
            <div className="node-glow"></div>
            <i className="fas fa-brain"></i>
          </div>
          <div className="agent-communication-node anthropic" style={{ top: '25%', left: '85%', animationDelay: '2s' }}>
            <div className="node-glow"></div>
            <i className="fas fa-comments"></i>
          </div>
          <div className="agent-communication-node gemini" style={{ top: '65%', left: '80%', animationDelay: '4s' }}>
            <div className="node-glow"></div>
            <i className="fas fa-code"></i>
          </div>
          <div className="agent-communication-node groq" style={{ top: '75%', left: '15%', animationDelay: '6s' }}>
            <div className="node-glow"></div>
            <i className="fas fa-bolt"></i>
          </div>
          <div className="agent-communication-node huggingface" style={{ top: '35%', left: '5%', animationDelay: '8s' }}>
            <div className="node-glow"></div>
            <i className="fas fa-robot"></i>
          </div>
          <div className="agent-communication-node openai" style={{ top: '55%', left: '90%', animationDelay: '10s' }}>
            <div className="node-glow"></div>
            <i className="fas fa-chart-line"></i>
          </div>
          
          {/* Animated Bot Communication Messages */}
          <div className="bot-message-bubble openai" style={{ top: '10%', left: '12%', animationDelay: '2s' }}>
            <div className="message-content">
              <span className="bot-name">GPT-4</span>
              <span className="bot-message">Analyzing user intent...</span>
            </div>
            <div className="message-tail"></div>
          </div>
          
          <div className="bot-message-bubble anthropic" style={{ top: '20%', left: '70%', animationDelay: '4s' }}>
            <div className="message-content">
              <span className="bot-name">Claude</span>
              <span className="bot-message">Processing context</span>
            </div>
            <div className="message-tail"></div>
          </div>
          
          <div className="bot-message-bubble gemini" style={{ top: '60%', left: '75%', animationDelay: '6s' }}>
            <div className="message-content">
              <span className="bot-name">Gemini</span>
              <span className="bot-message">Code review complete ✓</span>
            </div>
            <div className="message-tail"></div>
          </div>
          
          <div className="bot-message-bubble groq" style={{ top: '70%', left: '18%', animationDelay: '8s' }}>
            <div className="message-content">
              <span className="bot-name">Llama 3.1</span>
              <span className="bot-message">Processing 12K tokens/sec</span>
            </div>
            <div className="message-tail"></div>
          </div>
          
          <div className="bot-message-bubble huggingface" style={{ top: '30%', left: '8%', animationDelay: '10s' }}>
            <div className="message-content">
              <span className="bot-name">Mixtral</span>
              <span className="bot-message">Model fine-tuning...</span>
            </div>
            <div className="message-tail"></div>
          </div>
          
          <div className="bot-message-bubble openai" style={{ top: '50%', left: '85%', animationDelay: '12s' }}>
            <div className="message-content">
              <span className="bot-name">GPT-3.5</span>
              <span className="bot-message">Generating response</span>
            </div>
            <div className="message-tail"></div>
          </div>
          
          <div className="bot-message-bubble anthropic" style={{ top: '40%', left: '20%', animationDelay: '14s' }}>
            <div className="message-content">
              <span className="bot-name">Claude Haiku</span>
              <span className="bot-message">Summarizing data...</span>
            </div>
            <div className="message-tail"></div>
          </div>
          
          <div className="bot-message-bubble gemini" style={{ top: '80%', left: '60%', animationDelay: '16s' }}>
            <div className="message-content">
              <span className="bot-name">Gemini Flash</span>
              <span className="bot-message">Real-time analysis</span>
            </div>
            <div className="message-tail"></div>
          </div>
          
          {/* Inter-Agent Communication Lines */}
          <div className="communication-line" style={{ top: '18%', left: '15%', width: '50%', transform: 'rotate(10deg)', animationDelay: '3s' }}></div>
          <div className="communication-line" style={{ top: '45%', left: '10%', width: '70%', transform: 'rotate(-15deg)', animationDelay: '7s' }}></div>
          <div className="communication-line" style={{ top: '68%', left: '25%', width: '45%', transform: 'rotate(25deg)', animationDelay: '11s' }}></div>
          <div className="communication-line" style={{ top: '35%', left: '50%', width: '30%', transform: 'rotate(-5deg)', animationDelay: '15s' }}></div>
          
          {/* Data Flow Particles */}
          <div className="data-particle" style={{ top: '22%', left: '20%', animationDelay: '4s' }}></div>
          <div className="data-particle" style={{ top: '52%', left: '70%', animationDelay: '8s' }}></div>
          <div className="data-particle" style={{ top: '72%', left: '40%', animationDelay: '12s' }}></div>
          <div className="data-particle" style={{ top: '42%', left: '85%', animationDelay: '16s' }}></div>
          
          {/* Typing Indicators for Active Communication */}
          <div className="agent-typing-indicator" style={{ top: '28%', left: '65%', animationDelay: '5s' }}>
            <div className="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          
          <div className="agent-typing-indicator" style={{ top: '62%', left: '25%', animationDelay: '9s' }}>
            <div className="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          
          <div className="agent-typing-indicator" style={{ top: '48%', left: '78%', animationDelay: '13s' }}>
            <div className="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-20">
          <div className="text-center max-w-5xl mx-auto">
            <div className={`transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
              {/* Enhanced Main Title */}
              <div className="hero-title-section mb-16">
                <h1 className="hero-main-title">
                  <span className="title-line-1">Build Your</span>
                  <span className="title-line-2 gradient-text-enhanced">Agentic Ecosystem</span>
                </h1>
                <p className="hero-subtitle">
                  Design intelligent AI agents that collaborate across workflows, deploy instantly to any channel,
                  and orchestrate complex automations with enterprise-grade reliability.
                </p>
              </div>
              
              {/* Enhanced Feature Pills */}
              <div className={`hero-features-grid mb-16 transition-all duration-1200 delay-300 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
                <div className="feature-pill-enhanced">
                  <div className="feature-icon-wrapper">
                    <i className="fas fa-brain text-blue-400"></i>
                  </div>
                  <div className="feature-content">
                    <h3 className="feature-title">Multi-Model Support</h3>
                    <p className="feature-description">OpenAI, Anthropic, Gemini, Groq & more</p>
                  </div>
                </div>
                
                <div className="feature-pill-enhanced">
                  <div className="feature-icon-wrapper">
                    <i className="fas fa-network-wired text-purple-400"></i>
                  </div>
                  <div className="feature-content">
                    <h3 className="feature-title">Cross-Channel Deploy</h3>
                    <p className="feature-description">WhatsApp, Slack, Telegram & 15+ channels</p>
                  </div>
                </div>
                
                <div className="feature-pill-enhanced">
                  <div className="feature-icon-wrapper">
                    <i className="fas fa-cogs text-green-400"></i>
                  </div>
                  <div className="feature-content">
                    <h3 className="feature-title">Workflow Orchestration</h3>
                    <p className="feature-description">Complex automations with 50+ integrations</p>
                  </div>
                </div>
              </div>
              
              {/* Enhanced CTA Buttons */}
              <div className={`hero-cta-section mt-16 transition-all duration-1200 delay-500 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
                <div className="cta-buttons-wrapper">
                  {session ? (
                    <button className="cta-button primary">
                      <i className="fas fa-rocket"></i>
                      <span>Launch Your Ecosystem</span>
                    </button>
                  ) : (
                    <button
                      onClick={handleGoogleSignIn}
                      className="cta-button primary"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="white"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="white"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="white"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="white"/>
                      </svg>
                      <span>Get Started with Google</span>
                    </button>
                  )}
                  
                  <button className="cta-button secondary">
                    <i className="fas fa-play"></i>
                    <span>Watch Demo</span>
                  </button>
                </div>
                
                <div className="hero-trust-indicators">
                  <div className="trust-item">
                    <i className="fas fa-shield-alt"></i>
                    <span>Enterprise Security</span>
                  </div>
                  <div className="trust-item">
                    <i className="fas fa-infinity"></i>
                    <span>Unlimited Agents</span>
                  </div>
                  <div className="trust-item">
                    <i className="fas fa-rocket"></i>
                    <span>Deploy in Minutes</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Features Section - Workflow Diagram */}
      <section id="features" className="relative z-10 px-6 sm:px-8 py-20">
        <div className="max-w-7xl mx-auto">
          <div className={`text-center mb-16 scroll-animate fade-up ${isVisible('features-header') ? 'visible' : ''}`} id="features-header">
            <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
              Complete <span className="gradient-text">Agentic Platform</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Build workflows and ecosystems with AI agents that integrate across channels, tools, and AI models.
            </p>
          </div>
          
          {/* Workflow Diagram Container */}
          <div className={`workflow-diagram relative overflow-hidden rounded-3xl glass-card p-8 md:p-12 scroll-animate scale-in ${isVisible('workflow-diagram') ? 'visible' : ''}`} id="workflow-diagram">
            {/* Background Grid */}
            <div className="absolute inset-0 opacity-20">
              <div className="workflow-grid"></div>
            </div>
            
            {/* Workflow Visualization */}
            <div className="relative min-h-[600px] md:min-h-[700px]">
              
              {/* Mobile: Flexbox Layout, Desktop: Absolute Layout */}
              <div className="hidden md:contents">
                {/* Input Sources */}
                <div className="absolute top-8 left-8 space-y-4">
                  <div className="workflow-node input-node" data-delay="0.5s">
                    <div className="node-icon">
                      <i className="fas fa-envelope text-blue-400"></i>
                    </div>
                    <div className="node-label">Email</div>
                    <div className="node-pulse"></div>
                  </div>
                  <div className="workflow-node input-node" data-delay="1s">
                    <div className="node-icon">
                      <i className="fas fa-comments text-green-400"></i>
                    </div>
                    <div className="node-label">Chat</div>
                    <div className="node-pulse"></div>
                  </div>
                  <div className="workflow-node input-node" data-delay="1.5s">
                    <div className="node-icon">
                      <i className="fas fa-file-alt text-purple-400"></i>
                    </div>
                    <div className="node-label">Documents</div>
                    <div className="node-pulse"></div>
                  </div>
                </div>
                
                {/* Processing Agents */}
                <div className="absolute top-16 left-1/2 transform -translate-x-1/2 space-y-6">
                  {/* Agent 1 - Intake */}
                  <div className="workflow-agent-card openai" data-delay="2s">
                    <div className="agent-header">
                      <div className="agent-avatar">
                        <i className="fas fa-robot"></i>
                      </div>
                      <div className="agent-info">
                        <div className="agent-name">Intake Agent</div>
                        <div className="agent-model">GPT-4</div>
                      </div>
                      <div className="agent-status active">●</div>
                    </div>
                    <div className="agent-task">
                      <div className="task-text">Processing incoming requests...</div>
                      <div className="task-progress">
                        <div className="progress-bar" style={{ width: '75%' }}></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Agent 2 - Analysis */}
                  <div className="workflow-agent-card anthropic" data-delay="2.5s">
                    <div className="agent-header">
                      <div className="agent-avatar">
                        <i className="fas fa-brain"></i>
                      </div>
                      <div className="agent-info">
                        <div className="agent-name">Analysis Agent</div>
                        <div className="agent-model">Claude</div>
                      </div>
                      <div className="agent-status active">●</div>
                    </div>
                    <div className="agent-task">
                      <div className="task-text">Analyzing context & sentiment...</div>
                      <div className="task-progress">
                        <div className="progress-bar" style={{ width: '60%' }}></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Agent 3 - Action */}
                  <div className="workflow-agent-card gemini" data-delay="3s">
                    <div className="agent-header">
                      <div className="agent-avatar">
                        <i className="fas fa-cogs"></i>
                      </div>
                      <div className="agent-info">
                        <div className="agent-name">Action Agent</div>
                        <div className="agent-model">Gemini Pro</div>
                      </div>
                      <div className="agent-status active">●</div>
                    </div>
                    <div className="agent-task">
                      <div className="task-text">Executing workflow actions...</div>
                      <div className="task-progress">
                        <div className="progress-bar" style={{ width: '90%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Tool Integrations */}
                <div className="absolute top-32 right-8 space-y-4">
                  <div className="integration-node" data-delay="3.5s">
                    <div className="integration-icon">
                      <i className="fab fa-github text-gray-300"></i>
                    </div>
                    <div className="integration-label">GitHub</div>
                  </div>
                  <div className="integration-node" data-delay="4s">
                    <div className="integration-icon">
                      <i className="fas fa-ticket-alt text-blue-400"></i>
                    </div>
                    <div className="integration-label">Jira</div>
                  </div>
                  <div className="integration-node" data-delay="4.5s">
                    <div className="integration-icon">
                      <i className="fas fa-database text-green-400"></i>
                    </div>
                    <div className="integration-label">Database</div>
                  </div>
                  <div className="integration-node" data-delay="5s">
                    <div className="integration-icon">
                      <i className="fab fa-aws text-orange-400"></i>
                    </div>
                    <div className="integration-label">AWS</div>
                  </div>
                </div>
                
                {/* Output Channels */}
                <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-6">
                  <div className="channel-node" data-delay="5.5s">
                    <div className="channel-icon">
                      <i className="fab fa-whatsapp text-green-400"></i>
                    </div>
                    <div className="channel-label">WhatsApp</div>
                    <div className="channel-pulse"></div>
                  </div>
                  <div className="channel-node" data-delay="6s">
                    <div className="channel-icon">
                      <i className="fab fa-slack text-purple-400"></i>
                    </div>
                    <div className="channel-label">Slack</div>
                    <div className="channel-pulse"></div>
                  </div>
                  <div className="channel-node" data-delay="6.5s">
                    <div className="channel-icon">
                      <i className="fab fa-telegram text-blue-400"></i>
                    </div>
                    <div className="channel-label">Telegram</div>
                    <div className="channel-pulse"></div>
                  </div>
                  <div className="channel-node" data-delay="7s">
                    <div className="channel-icon">
                      <i className="fas fa-globe text-cyan-400"></i>
                    </div>
                    <div className="channel-label">Website</div>
                    <div className="channel-pulse"></div>
                  </div>
                </div>
              </div>
              
              {/* Mobile Layout - Flexbox */}
              <div className="md:hidden flex flex-col space-y-8 py-4">
                {/* Status Card */}
                <div className="self-end">
                  <div className="workflow-status-card">
                    <div className="status-header">
                      <div className="status-icon">
                        <i className="fas fa-heartbeat text-green-400"></i>
                      </div>
                      <div className="status-text">Workflow Active</div>
                    </div>
                    <div className="status-metrics">
                      <div className="metric">
                        <span className="metric-label">Agents</span>
                        <span className="metric-value gradient-text">3/3</span>
                      </div>
                      <div className="metric">
                        <span className="metric-label">Throughput</span>
                        <span className="metric-value gradient-text">45/min</span>
                      </div>
                      <div className="metric">
                        <span className="metric-label">Channels</span>
                        <span className="metric-value gradient-text">4</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Input Sources */}
                <div className="flex flex-col items-center space-y-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Input Sources</h3>
                  <div className="flex space-x-4">
                    <div className="workflow-node input-node">
                      <div className="node-icon">
                        <i className="fas fa-envelope text-blue-400"></i>
                      </div>
                      <div className="node-label">Email</div>
                      <div className="node-pulse"></div>
                    </div>
                    <div className="workflow-node input-node">
                      <div className="node-icon">
                        <i className="fas fa-comments text-green-400"></i>
                      </div>
                      <div className="node-label">Chat</div>
                      <div className="node-pulse"></div>
                    </div>
                    <div className="workflow-node input-node">
                      <div className="node-icon">
                        <i className="fas fa-file-alt text-purple-400"></i>
                      </div>
                      <div className="node-label">Documents</div>
                      <div className="node-pulse"></div>
                    </div>
                  </div>
                </div>
                
                {/* Processing Agents */}
                <div className="flex flex-col items-center space-y-4">
                  <h3 className="text-lg font-semibold text-white mb-2">AI Agents</h3>
                  <div className="flex flex-col space-y-4 w-full max-w-sm">
                    <div className="workflow-agent-card openai">
                      <div className="agent-header">
                        <div className="agent-avatar">
                          <i className="fas fa-robot"></i>
                        </div>
                        <div className="agent-info">
                          <div className="agent-name">Intake Agent</div>
                          <div className="agent-model">GPT-4</div>
                        </div>
                        <div className="agent-status active">●</div>
                      </div>
                      <div className="agent-task">
                        <div className="task-text">Processing incoming requests...</div>
                        <div className="task-progress">
                          <div className="progress-bar" style={{ width: '75%' }}></div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="workflow-agent-card anthropic">
                      <div className="agent-header">
                        <div className="agent-avatar">
                          <i className="fas fa-brain"></i>
                        </div>
                        <div className="agent-info">
                          <div className="agent-name">Analysis Agent</div>
                          <div className="agent-model">Claude</div>
                        </div>
                        <div className="agent-status active">●</div>
                      </div>
                      <div className="agent-task">
                        <div className="task-text">Analyzing context & sentiment...</div>
                        <div className="task-progress">
                          <div className="progress-bar" style={{ width: '60%' }}></div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="workflow-agent-card gemini">
                      <div className="agent-header">
                        <div className="agent-avatar">
                          <i className="fas fa-cogs"></i>
                        </div>
                        <div className="agent-info">
                          <div className="agent-name">Action Agent</div>
                          <div className="agent-model">Gemini Pro</div>
                        </div>
                        <div className="agent-status active">●</div>
                      </div>
                      <div className="agent-task">
                        <div className="task-text">Executing workflow actions...</div>
                        <div className="task-progress">
                          <div className="progress-bar" style={{ width: '90%' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Tool Integrations */}
                <div className="flex flex-col items-center space-y-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Tool Integrations</h3>
                  <div className="flex flex-wrap gap-4 justify-center">
                    <div className="integration-node">
                      <div className="integration-icon">
                        <i className="fab fa-github text-gray-300"></i>
                      </div>
                      <div className="integration-label">GitHub</div>
                    </div>
                    <div className="integration-node">
                      <div className="integration-icon">
                        <i className="fas fa-ticket-alt text-blue-400"></i>
                      </div>
                      <div className="integration-label">Jira</div>
                    </div>
                    <div className="integration-node">
                      <div className="integration-icon">
                        <i className="fas fa-database text-green-400"></i>
                      </div>
                      <div className="integration-label">Database</div>
                    </div>
                    <div className="integration-node">
                      <div className="integration-icon">
                        <i className="fab fa-aws text-orange-400"></i>
                      </div>
                      <div className="integration-label">AWS</div>
                    </div>
                  </div>
                </div>
                
                {/* Output Channels */}
                <div className="flex flex-col items-center space-y-4">
                  <h3 className="text-lg font-semibold text-white mb-2">Output Channels</h3>
                  <div className="flex flex-wrap gap-4 justify-center">
                    <div className="channel-node">
                      <div className="channel-icon">
                        <i className="fab fa-whatsapp text-green-400"></i>
                      </div>
                      <div className="channel-label">WhatsApp</div>
                      <div className="channel-pulse"></div>
                    </div>
                    <div className="channel-node">
                      <div className="channel-icon">
                        <i className="fab fa-slack text-purple-400"></i>
                      </div>
                      <div className="channel-label">Slack</div>
                      <div className="channel-pulse"></div>
                    </div>
                    <div className="channel-node">
                      <div className="channel-icon">
                        <i className="fab fa-telegram text-blue-400"></i>
                      </div>
                      <div className="channel-label">Telegram</div>
                      <div className="channel-pulse"></div>
                    </div>
                    <div className="channel-node">
                      <div className="channel-icon">
                        <i className="fas fa-globe text-cyan-400"></i>
                      </div>
                      <div className="channel-label">Website</div>
                      <div className="channel-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Desktop-only elements */}
              <div className="hidden md:contents">
                {/* Data Flow Lines */}
                <div className="workflow-connections">
                  {/* Input to Processing */}
                  <div className="flow-line from-input to-processing" data-delay="2.5s">
                    <div className="data-packet openai"></div>
                  </div>
                  
                  {/* Between Processing Agents */}
                  <div className="flow-line between-agents agent-1-to-2" data-delay="3s">
                    <div className="data-packet anthropic"></div>
                  </div>
                  <div className="flow-line between-agents agent-2-to-3" data-delay="3.5s">
                    <div className="data-packet gemini"></div>
                  </div>
                  
                  {/* Processing to Tools */}
                  <div className="flow-line from-processing to-tools" data-delay="4s">
                    <div className="data-packet groq"></div>
                  </div>
                  
                  {/* Processing to Output */}
                  <div className="flow-line from-processing to-output" data-delay="5s">
                    <div className="data-packet huggingface"></div>
                  </div>
                </div>
                
                {/* Workflow Status */}
                <div className="absolute top-8 right-8">
                  <div className="workflow-status-card">
                    <div className="status-header">
                      <div className="status-icon">
                        <i className="fas fa-heartbeat text-green-400"></i>
                      </div>
                      <div className="status-text">Workflow Active</div>
                    </div>
                    <div className="status-metrics">
                      <div className="metric">
                        <span className="metric-label">Agents</span>
                        <span className="metric-value gradient-text">3/3</span>
                      </div>
                      <div className="metric">
                        <span className="metric-label">Throughput</span>
                        <span className="metric-value gradient-text">45/min</span>
                      </div>
                      <div className="metric">
                        <span className="metric-label">Channels</span>
                        <span className="metric-value gradient-text">4</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Floating Context Cards */}
                <div className="floating-context-card" style={{ top: '25%', left: '75%' }} data-delay="8s">
                  <div className="context-text">
                    <i className="fas fa-share-alt text-blue-400 mr-2"></i>
                    Agents share context seamlessly
                  </div>
                </div>
                
                <div className="floating-context-card" style={{ top: '70%', left: '15%' }} data-delay="8.5s">
                  <div className="context-text">
                    <i className="fas fa-bolt text-purple-400 mr-2"></i>
                    Real-time processing across channels
                  </div>
                </div>
                
                <div className="floating-context-card" style={{ top: '45%', left: '85%' }} data-delay="9s">
                  <div className="context-text">
                    <i className="fas fa-infinity text-green-400 mr-2"></i>
                    Infinite scalability
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="relative z-10 px-6 sm:px-8 py-20">
        <div className="max-w-7xl mx-auto">
          <div className={`glass-card rounded-3xl p-12 scroll-animate fade-up ${isVisible('stats-section') ? 'visible' : ''}`} id="stats-section">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-12 text-center">
              <div>
                <div className="text-4xl sm:text-5xl font-bold gradient-text mb-4">5+</div>
                <div className="text-gray-300 text-lg">AI Model Providers</div>
                <div className="text-sm text-gray-400 mt-2">OpenAI, Anthropic, Gemini, Groq, HuggingFace</div>
              </div>
              <div>
                <div className="text-4xl sm:text-5xl font-bold gradient-text mb-4">15+</div>
                <div className="text-gray-300 text-lg">Channel Integrations</div>
                <div className="text-sm text-gray-400 mt-2">WhatsApp, Telegram, Slack, Web, API</div>
              </div>
              <div>
                <div className="text-4xl sm:text-5xl font-bold gradient-text mb-4">50+</div>
                <div className="text-gray-300 text-lg">Tool Integrations</div>
                <div className="text-sm text-gray-400 mt-2">GitHub, Jira, AWS, MongoDB, Gmail</div>
              </div>
              <div>
                <div className="text-4xl sm:text-5xl font-bold gradient-text mb-4">∞</div>
                <div className="text-gray-300 text-lg">Agent Workflows</div>
                <div className="text-sm text-gray-400 mt-2">Unlimited ecosystem possibilities</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section className="relative z-10 px-6 sm:px-8 py-32 overflow-hidden">
        {/* Enhanced Background Effects */}
        <div className="absolute inset-0 overflow-hidden opacity-40">
          {/* Animated Grid Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900/5 via-purple-900/5 to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-900/10 via-transparent to-transparent"></div>
          
          {/* Floating Gradient Orbs */}
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/8 rounded-full blur-3xl animate-float"></div>
          <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-purple-500/8 rounded-full blur-3xl animate-float" style={{ animationDelay: '3s' }}></div>
          <div className="absolute bottom-1/4 left-1/3 w-48 h-48 bg-cyan-500/8 rounded-full blur-3xl animate-float" style={{ animationDelay: '6s' }}></div>
          
          {/* Subtle Agent Communication Animation */}
          <div className="cta-agent-node openai" style={{ top: '20%', left: '10%', animationDelay: '0s' }}>
            <div className="node-glow"></div>
            <i className="fas fa-rocket"></i>
          </div>
          <div className="cta-agent-node anthropic" style={{ top: '15%', right: '15%', animationDelay: '2s' }}>
            <div className="node-glow"></div>
            <i className="fas fa-bolt"></i>
          </div>
          <div className="cta-agent-node gemini" style={{ bottom: '25%', left: '8%', animationDelay: '4s' }}>
            <div className="node-glow"></div>
            <i className="fas fa-cogs"></i>
          </div>
          <div className="cta-agent-node groq" style={{ bottom: '20%', right: '12%', animationDelay: '6s' }}>
            <div className="node-glow"></div>
            <i className="fas fa-network-wired"></i>
          </div>
          
          {/* Data Flow Lines */}
          <div className="cta-flow-line" style={{ top: '25%', left: '20%', width: '45%', transform: 'rotate(15deg)', animationDelay: '1s' }}></div>
          <div className="cta-flow-line" style={{ bottom: '30%', right: '25%', width: '40%', transform: 'rotate(-10deg)', animationDelay: '5s' }}></div>
        </div>
        
        <div className={`max-w-6xl mx-auto text-center relative z-20 scroll-animate fade-up ${isVisible('cta-section') ? 'visible' : ''}`} id="cta-section">
          {/* Enhanced Main Container */}
          <div className="cta-main-container">
            {/* Enhanced Title Section */}
            <div className="cta-title-section mb-12">
              <h2 className="cta-main-title">
                <span className="cta-title-line-1">Ready to Build Your</span>
                <span className="cta-title-line-2 gradient-text-enhanced">Agent Empire</span>
                <span className="cta-title-accent">?</span>
              </h2>
              <p className="cta-subtitle">
                Start creating intelligent agent workflows and ecosystems that work across all your favorite tools and channels.
              </p>
            </div>
            
            {/* Enhanced Feature Highlights */}
            <div className="cta-features-showcase mb-16">
              <div className="cta-feature-highlight">
                <div className="feature-highlight-icon">
                  <i className="fas fa-bolt text-yellow-400"></i>
                </div>
                <div className="feature-highlight-text">
                  <span className="highlight-title">Deploy in Minutes</span>
                  <span className="highlight-description">Zero-config setup</span>
                </div>
              </div>
              <div className="cta-feature-highlight">
                <div className="feature-highlight-icon">
                  <i className="fas fa-infinity text-blue-400"></i>
                </div>
                <div className="feature-highlight-text">
                  <span className="highlight-title">Unlimited Scale</span>
                  <span className="highlight-description">Enterprise-ready</span>
                </div>
              </div>
              <div className="cta-feature-highlight">
                <div className="feature-highlight-icon">
                  <i className="fas fa-shield-alt text-green-400"></i>
                </div>
                <div className="feature-highlight-text">
                  <span className="highlight-title">Bank-Grade Security</span>
                  <span className="highlight-description">SOC 2 compliant</span>
                </div>
              </div>
            </div>
            
            {/* Enhanced CTA Buttons */}
            <div className="cta-buttons-section mb-12">
              <div className="cta-buttons-enhanced">
                {session ? (
                  <button className="cta-button-enhanced primary">
                    <div className="button-icon">
                      <i className="fas fa-rocket"></i>
                    </div>
                    <div className="button-content">
                      <span className="button-text">Launch Your Ecosystem</span>
                      <span className="button-subtext">Build your first agent</span>
                    </div>
                    <div className="button-arrow">
                      <i className="fas fa-arrow-right"></i>
                    </div>
                  </button>
                ) : (
                  <button
                    onClick={handleGoogleSignIn}
                    className="cta-button-enhanced primary"
                  >
                    <div className="button-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="white"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="white"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="white"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="white"/>
                      </svg>
                    </div>
                    <div className="button-content">
                      <span className="button-text">Get Started with Google</span>
                      <span className="button-subtext">Free forever plan</span>
                    </div>
                    <div className="button-arrow">
                      <i className="fas fa-arrow-right"></i>
                    </div>
                  </button>
                )}
                
                <button className="cta-button-enhanced secondary">
                  <div className="button-icon">
                    <i className="fas fa-play"></i>
                  </div>
                  <div className="button-content">
                    <span className="button-text">Watch Demo</span>
                    <span className="button-subtext">See it in action</span>
                  </div>
                  <div className="button-arrow">
                    <i className="fas fa-external-link-alt"></i>
                  </div>
                </button>
              </div>
            </div>
            
            {/* Enhanced Social Proof */}
            <div className="cta-social-proof">
              <div className="social-proof-header">
                <span className="proof-text">Trusted by developers worldwide</span>
                <div className="proof-stats">
                  <div className="stat-item">
                    <div className="stat-number gradient-text">50K+</div>
                    <div className="stat-label">Active Agents</div>
                  </div>
                  <div className="stat-divider"></div>
                  <div className="stat-item">
                    <div className="stat-number gradient-text">10M+</div>
                    <div className="stat-label">Messages Processed</div>
                  </div>
                  <div className="stat-divider"></div>
                  <div className="stat-item">
                    <div className="stat-number gradient-text">99.9%</div>
                    <div className="stat-label">Uptime</div>
                  </div>
                </div>
              </div>
              
              <div className="social-proof-badges">
                <div className="proof-badge">
                  <i className="fas fa-star text-yellow-400"></i>
                  <span>SOC 2 Compliant</span>
                </div>
                <div className="proof-badge">
                  <i className="fas fa-shield-alt text-green-400"></i>
                  <span>Enterprise Ready</span>
                </div>
                <div className="proof-badge">
                  <i className="fas fa-clock text-blue-400"></i>
                  <span>24/7 Support</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 px-6 sm:px-8 py-12 border-t border-white/10">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 via-purple-500 to-cyan-500 flex items-center justify-center shadow-lg shadow-blue-500/25 border border-white/10">
                <Image
                  src="/logo/logo-light.svg"
                  alt="Botmani"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-black tracking-wider bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                  BOTMANI
                </span>
                <span className="text-[8px] text-gray-400 tracking-[0.2em] uppercase font-medium -mt-1">
                  AI ECOSYSTEM
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <a href="#privacy" className="text-gray-400 hover:text-white transition-colors">Privacy</a>
              <a href="#terms" className="text-gray-400 hover:text-white transition-colors">Terms</a>
              <a href="#contact" className="text-gray-400 hover:text-white transition-colors">Contact</a>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-white/10 text-center text-gray-400">
            <p>&copy; 2025 Botmani. All rights reserved.</p>
          </div>
        </div>
      </footer>
      </div>
    </>
  );
}
