@import "tailwindcss";

:root {
  --background: #000000;
  --foreground: #ffffff;
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --accent-primary: #3b82f6;
  --accent-secondary: #8b5cf6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-glass-bg: var(--glass-bg);
  --color-glass-border: var(--glass-border);
  --color-accent-primary: var(--accent-primary);
  --color-accent-secondary: var(--accent-secondary);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  overflow-x: hidden;
}

/* Glassmorphism utilities */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.glass-card {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.gradient-text {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--glass-bg);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--glass-border);
}

/* Enhanced gradient glassy scrollbar for sidebar */
.sidebar-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.sidebar-scrollbar::-webkit-scrollbar-track {
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  margin: 2px;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(59, 130, 246, 0.4) 0%,
    rgba(139, 92, 246, 0.5) 50%,
    rgba(59, 130, 246, 0.4) 100%);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  transition: all 0.3s ease;
  box-shadow:
    0 2px 8px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg,
    rgba(59, 130, 246, 0.6) 0%,
    rgba(139, 92, 246, 0.7) 50%,
    rgba(59, 130, 246, 0.6) 100%);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 0 1px rgba(139, 92, 246, 0.3);
  transform: scale(1.1);
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg,
    rgba(59, 130, 246, 0.8) 0%,
    rgba(139, 92, 246, 0.9) 50%,
    rgba(59, 130, 246, 0.8) 100%);
  transform: scale(1.05);
}

.sidebar-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox scrollbar styling */
.sidebar-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.4) rgba(255, 255, 255, 0.05);
}

/* Enhanced scrollbar for Firefox with custom properties */
@supports (scrollbar-color: auto) {
  .sidebar-scrollbar {
    scrollbar-color:
      linear-gradient(180deg, rgba(59, 130, 246, 0.4), rgba(139, 92, 246, 0.5))
      linear-gradient(180deg, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.05));
  }
}

/* Hidden scrollbar for collapsed sidebar */
.sidebar-collapsed::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.sidebar-collapsed::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-collapsed::-webkit-scrollbar-thumb {
  background: transparent;
}

.sidebar-collapsed {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Agent Ecosystem Animation */
.agent-ecosystem {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  overflow: hidden;
}

.agent-node {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  backdrop-filter: blur(10px);
  animation: agent-pulse 3s ease-in-out infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--accent-primary);
}

.agent-node i {
  font-size: 20px;
  animation: bot-blink 4s ease-in-out infinite;
}

.agent-node.openai {
  background: linear-gradient(135deg, rgba(16, 163, 127, 0.2), rgba(16, 163, 127, 0.1));
  border-color: rgba(16, 163, 127, 0.3);
}

.agent-node.anthropic {
  background: linear-gradient(135deg, rgba(196, 109, 57, 0.2), rgba(196, 109, 57, 0.1));
  border-color: rgba(196, 109, 57, 0.3);
}

.agent-node.gemini {
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.2), rgba(66, 133, 244, 0.1));
  border-color: rgba(66, 133, 244, 0.3);
}

.agent-node.groq {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 107, 107, 0.1));
  border-color: rgba(255, 107, 107, 0.3);
}

.agent-node.huggingface {
  background: linear-gradient(135deg, rgba(255, 206, 84, 0.2), rgba(255, 206, 84, 0.1));
  border-color: rgba(255, 206, 84, 0.3);
}

@keyframes bot-blink {
  0%, 90%, 100% { opacity: 1; }
  95% { opacity: 0.6; }
}

@keyframes agent-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

@keyframes agent-core-pulse {
  0%, 100% { opacity: 0.8; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

.connection-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
  opacity: 0.4;
  animation: data-flow 4s ease-in-out infinite;
}

@keyframes data-flow {
  0% { opacity: 0; transform: scaleX(0); }
  50% { opacity: 0.6; transform: scaleX(1); }
  100% { opacity: 0; transform: scaleX(0); }
}

.llm-chatbox {
  position: absolute;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 10px 14px;
  font-size: 11px;
  color: white;
  backdrop-filter: blur(15px);
  animation: chatbox-appear 8s ease-in-out infinite;
  opacity: 0;
  transform-origin: bottom left;
  white-space: nowrap;
  font-weight: 500;
  min-width: 140px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.llm-chatbox::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 16px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid var(--glass-bg);
}

.llm-chatbox .model-name {
  font-size: 9px;
  color: var(--accent-primary);
  font-weight: 600;
  margin-bottom: 2px;
  display: block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.llm-chatbox .message {
  color: #e5e7eb;
  font-size: 10px;
  line-height: 1.3;
}

.llm-chatbox.openai {
  border-color: rgba(16, 163, 127, 0.4);
  background: linear-gradient(135deg, rgba(16, 163, 127, 0.15), rgba(16, 163, 127, 0.05));
}

.llm-chatbox.openai .model-name {
  color: #10a37f;
}

.llm-chatbox.anthropic {
  border-color: rgba(196, 109, 57, 0.4);
  background: linear-gradient(135deg, rgba(196, 109, 57, 0.15), rgba(196, 109, 57, 0.05));
}

.llm-chatbox.anthropic .model-name {
  color: #c46d39;
}

.llm-chatbox.gemini {
  border-color: rgba(66, 133, 244, 0.4);
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.15), rgba(66, 133, 244, 0.05));
}

.llm-chatbox.gemini .model-name {
  color: #4285f4;
}

.llm-chatbox.groq {
  border-color: rgba(255, 107, 107, 0.4);
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(255, 107, 107, 0.05));
}

.llm-chatbox.groq .model-name {
  color: #ff6b6b;
}

.llm-chatbox.huggingface {
  border-color: rgba(255, 206, 84, 0.4);
  background: linear-gradient(135deg, rgba(255, 206, 84, 0.15), rgba(255, 206, 84, 0.05));
}

.llm-chatbox.huggingface .model-name {
  color: #ffce54;
}

@keyframes chatbox-appear {
  0%, 10% { opacity: 0; transform: scale(0.8) translateY(10px); }
  15%, 85% { opacity: 1; transform: scale(1) translateY(0); }
  90%, 100% { opacity: 0; transform: scale(0.8) translateY(-10px); }
}

.typing-indicator {
  position: absolute;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 8px 12px;
  backdrop-filter: blur(10px);
  animation: typing-appear 4s ease-in-out infinite;
  opacity: 0;
}

.typing-indicator::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 12px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid var(--glass-bg);
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background: var(--accent-primary);
  border-radius: 50%;
  animation: typing-bounce 1.4s ease-in-out infinite;
}

.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing-bounce {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-8px); }
}

@keyframes typing-appear {
  0%, 20% { opacity: 0; transform: scale(0.8); }
  25%, 75% { opacity: 1; transform: scale(1); }
  80%, 100% { opacity: 0; transform: scale(0.8); }
}

.ecosystem-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 20%, var(--accent-primary) 1px, transparent 1px),
    radial-gradient(circle at 80% 80%, var(--accent-secondary) 1px, transparent 1px);
  background-size: 60px 60px;
  opacity: 0.1;
  animation: grid-shift 20s linear infinite;
}

@keyframes grid-shift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

/* Scroll Animation Classes */
.scroll-animate {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-animate.visible {
  opacity: 1;
  transform: translateY(0);
}

.scroll-animate.slide-left {
  transform: translateX(-50px);
}

.scroll-animate.slide-left.visible {
  transform: translateX(0);
}

.scroll-animate.slide-right {
  transform: translateX(50px);
}

.scroll-animate.slide-right.visible {
  transform: translateX(0);
}

.scroll-animate.fade-up {
  transform: translateY(30px);
}

.scroll-animate.fade-up.visible {
  transform: translateY(0);
}

.scroll-animate.scale-in {
  transform: scale(0.8);
}

.scroll-animate.scale-in.visible {
  transform: scale(1);
}

.scroll-animate.delay-1 {
  transition-delay: 0.1s;
}

.scroll-animate.delay-2 {
  transition-delay: 0.2s;
}

.scroll-animate.delay-3 {
  transition-delay: 0.3s;
}

.scroll-animate.delay-4 {
  transition-delay: 0.4s;
}

.scroll-animate.delay-5 {
  transition-delay: 0.5s;
}

.scroll-animate.delay-6 {
  transition-delay: 0.6s;
}

/* Chatbox Feature Card Animations */
.chatbox-card {
  position: relative;
  overflow: hidden;
}

.chatbox-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.chatbox-card:hover::before {
  left: 100%;
}

.chat-message {
  opacity: 0;
  transform: translateY(10px);
  animation: chat-message-appear 0.8s ease-out forwards;
}

.chat-message.user {
  animation-delay: 0.2s;
}

.chat-message.bot {
  animation-delay: 0.8s;
}

@keyframes chat-message-appear {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.chat-avatar {
  animation: avatar-glow 2s ease-in-out infinite;
}

@keyframes avatar-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0);
  }
}

.chat-window-header {
  position: relative;
  overflow: hidden;
}

.chat-window-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: header-scan 3s ease-in-out infinite;
}

@keyframes header-scan {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: -100%; }
}

.typing-animation {
  display: inline-block;
  animation: typing 2s steps(40, end) infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

.cursor-blink {
  animation: cursor-blink 1s infinite;
}

@keyframes cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.chat-bubble {
  position: relative;
  animation: bubble-bounce 0.6s ease-out;
}

@keyframes bubble-bounce {
  0% {
    transform: scale(0.8) translateY(20px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) translateY(-5px);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.chat-bubble::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s;
}

.chat-bubble:hover::before {
  opacity: 1;
}

.gradient-text-animated {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary), var(--accent-primary));
  background-size: 300% 300%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-flow 3s ease infinite;
}

@keyframes gradient-flow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.window-controls {
  animation: controls-pulse 4s ease-in-out infinite;
}

@keyframes controls-pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.chat-card-glow {
  position: relative;
}

.chat-card-glow::after {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  background: linear-gradient(45deg,
    rgba(59, 130, 246, 0.3),
    rgba(139, 92, 246, 0.3),
    rgba(59, 130, 246, 0.3));
  background-size: 300% 300%;
  animation: glow-rotate 4s ease infinite;
  opacity: 0;
  transition: opacity 0.3s;
  z-index: -1;
}

.chat-card-glow:hover::after {
  opacity: 1;
}

@keyframes glow-rotate {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.staggered-animation {
  animation-fill-mode: both;
}

.staggered-animation:nth-child(1) { animation-delay: 0.1s; }
.staggered-animation:nth-child(2) { animation-delay: 0.2s; }
.staggered-animation:nth-child(3) { animation-delay: 0.3s; }
.staggered-animation:nth-child(4) { animation-delay: 0.4s; }
.staggered-animation:nth-child(5) { animation-delay: 0.5s; }
.staggered-animation:nth-child(6) { animation-delay: 0.6s; }

/* Workflow Diagram Styles - Optimized */
.workflow-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 40px 40px;
  /* Removed expensive continuous animation */
  opacity: 0.5;
  will-change: auto;
}

/* Performance optimization: reduce animations when not in focus */
.workflow-diagram:not(:hover) .agent-avatar,
.workflow-diagram:not(:hover) .progress-bar {
  animation-play-state: paused;
}

/* Hardware acceleration for better performance */
.workflow-diagram {
  transform: translateZ(0);
  contain: layout style paint;
}

/* Global button cursor styling */
button,
.cta-button,
.cta-button-enhanced,
.glass-card button,
input[type="button"],
input[type="submit"],
[role="button"],
.clickable {
  cursor: pointer !important;
}

/* Reduce animations for better performance and accessibility */
@media (prefers-reduced-motion: reduce) {
  .workflow-agent-card,
  .agent-avatar,
  .progress-bar,
  .status-icon,
  .floating-orb,
  .workflow-node {
    animation: none !important;
    transition: none !important;
  }
}

/* Disable complex animations on lower-end devices */
@media (max-width: 768px) and (max-resolution: 1.5dppx) {
  .agent-avatar,
  .progress-bar,
  .data-packet,
  .floating-orb {
    animation-duration: 6s !important;
    animation-iteration-count: 1 !important;
  }
}

.workflow-node {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 16px;
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: node-appear 0.8s ease-out both;
  cursor: pointer;
  transition: transform 0.2s ease;
  will-change: transform;
  contain: layout style;
}

.workflow-node:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

.workflow-node[data-delay] {
  animation-delay: var(--delay);
}

.workflow-node[data-delay="0.5s"] { animation-delay: 0.5s; }
.workflow-node[data-delay="1s"] { animation-delay: 1s; }
.workflow-node[data-delay="1.5s"] { animation-delay: 1.5s; }

.node-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.node-label {
  font-size: 10px;
  color: var(--foreground);
  font-weight: 500;
  text-align: center;
}

.node-pulse {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 18px;
  border: 2px solid var(--accent-primary);
  opacity: 0;
  animation: pulse-ring 2s ease-out infinite;
}

@keyframes node-appear {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.workflow-agent-card {
  position: relative;
  width: 300px;
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  padding: 16px;
  animation: agent-card-appear 0.8s ease-out both;
  transition: all 0.3s ease;
  will-change: transform;
  contain: layout style paint;
}

.workflow-agent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.workflow-agent-card[data-delay] {
  animation-delay: var(--delay);
}

.workflow-agent-card[data-delay="2s"] { animation-delay: 2s; }
.workflow-agent-card[data-delay="2.5s"] { animation-delay: 2.5s; }
.workflow-agent-card[data-delay="3s"] { animation-delay: 3s; }

.workflow-agent-card.openai {
  border-color: rgba(16, 163, 127, 0.4);
  background: linear-gradient(135deg, rgba(16, 163, 127, 0.1), var(--glass-bg));
}

.workflow-agent-card.anthropic {
  border-color: rgba(196, 109, 57, 0.4);
  background: linear-gradient(135deg, rgba(196, 109, 57, 0.1), var(--glass-bg));
}

.workflow-agent-card.gemini {
  border-color: rgba(66, 133, 244, 0.4);
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.1), var(--glass-bg));
}

.agent-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: var(--accent-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  animation: avatar-pulse 4s ease-in-out infinite;
  will-change: transform;
}

.agent-info {
  flex: 1;
}

.agent-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 2px;
}

.agent-model {
  font-size: 11px;
  color: var(--accent-primary);
  font-weight: 500;
}

.agent-status {
  font-size: 12px;
  color: #10b981;
  animation: status-blink 3s ease-in-out infinite;
}

.agent-task {
  border-top: 1px solid var(--glass-border);
  padding-top: 12px;
}

.task-text {
  font-size: 12px;
  color: var(--foreground);
  margin-bottom: 8px;
}

.task-progress {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 2px;
  animation: progress-flow 3s ease-in-out infinite;
  will-change: transform;
}

@keyframes agent-card-appear {
  from {
    opacity: 0;
    transform: translateX(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Optimized animations with hardware acceleration */
@keyframes avatar-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes status-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes progress-flow {
  0% { transform: translate3d(-100%, 0, 0); }
  100% { transform: translate3d(0, 0, 0); }
}

.integration-node {
  position: relative;
  width: 70px;
  height: 70px;
  border-radius: 14px;
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  backdrop-filter: blur(15px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: integration-appear 0.8s ease-out both;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.integration-node:hover {
  transform: scale(1.05);
}

.integration-node[data-delay="3.5s"] { animation-delay: 3.5s; }
.integration-node[data-delay="4s"] { animation-delay: 4s; }
.integration-node[data-delay="4.5s"] { animation-delay: 4.5s; }
.integration-node[data-delay="5s"] { animation-delay: 5s; }

.integration-icon {
  font-size: 20px;
  margin-bottom: 6px;
}

.integration-label {
  font-size: 9px;
  color: var(--foreground);
  font-weight: 500;
  text-align: center;
}

@keyframes integration-appear {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.channel-node {
  position: relative;
  width: 90px;
  height: 90px;
  border-radius: 18px;
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  backdrop-filter: blur(15px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: channel-appear 0.8s ease-out both;
  cursor: pointer;
  transition: all 0.3s ease;
}

.channel-node:hover {
  transform: scale(1.05);
}

.channel-node[data-delay="5.5s"] { animation-delay: 5.5s; }
.channel-node[data-delay="6s"] { animation-delay: 6s; }
.channel-node[data-delay="6.5s"] { animation-delay: 6.5s; }
.channel-node[data-delay="7s"] { animation-delay: 7s; }

.channel-icon {
  font-size: 26px;
  margin-bottom: 8px;
}

.channel-label {
  font-size: 10px;
  color: var(--foreground);
  font-weight: 500;
  text-align: center;
}

.channel-pulse {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 20px;
  border: 2px solid var(--accent-secondary);
  opacity: 0;
  animation: pulse-ring 2s ease-out infinite;
  animation-delay: 1s;
}

@keyframes channel-appear {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.workflow-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.flow-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
  opacity: 0;
  animation: flow-line-appear 0.8s ease-out both;
}

.flow-line[data-delay="2.5s"] { animation-delay: 2.5s; }
.flow-line[data-delay="3s"] { animation-delay: 3s; }
.flow-line[data-delay="3.5s"] { animation-delay: 3.5s; }
.flow-line[data-delay="4s"] { animation-delay: 4s; }
.flow-line[data-delay="5s"] { animation-delay: 5s; }

.flow-line.from-input {
  top: 15%;
  left: 15%;
  width: 30%;
  height: 2px;
  transform: rotate(15deg);
}

.flow-line.between-agents {
  left: 50%;
  width: 2px;
  height: 80px;
  background: linear-gradient(180deg, transparent, var(--accent-primary), transparent);
}

.flow-line.agent-1-to-2 {
  top: 25%;
}

.flow-line.agent-2-to-3 {
  top: 45%;
}

.flow-line.from-processing {
  top: 35%;
  left: 60%;
  width: 25%;
  height: 2px;
  transform: rotate(-10deg);
}

.flow-line.to-output {
  top: 75%;
  left: 45%;
  width: 10%;
  height: 2px;
}

.data-packet {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: packet-flow 3s ease-in-out infinite;
}

.data-packet.openai {
  background: #10a37f;
  box-shadow: 0 0 8px #10a37f;
}

.data-packet.anthropic {
  background: #c46d39;
  box-shadow: 0 0 8px #c46d39;
}

.data-packet.gemini {
  background: #4285f4;
  box-shadow: 0 0 8px #4285f4;
}

.data-packet.groq {
  background: #ff6b6b;
  box-shadow: 0 0 8px #ff6b6b;
}

.data-packet.huggingface {
  background: #ffce54;
  box-shadow: 0 0 8px #ffce54;
}

@keyframes flow-line-appear {
  from {
    opacity: 0;
    transform: scaleX(0);
  }
  to {
    opacity: 0.6;
    transform: scaleX(1);
  }
}

@keyframes packet-flow {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 0;
  }
  20% {
    opacity: 0.8;
  }
  80% {
    opacity: 0.8;
  }
  100% {
    transform: translate3d(100px, 0, 0);
    opacity: 0;
  }
}

.workflow-status-card {
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  padding: 16px;
  min-width: 180px;
  animation: status-card-appear 0.8s ease-out both;
  animation-delay: 1.5s;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.status-icon {
  font-size: 16px;
  animation: heartbeat 4s ease-in-out infinite;
}

.status-text {
  font-size: 12px;
  color: var(--foreground);
  font-weight: 600;
}

.status-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 11px;
  color: var(--foreground);
  opacity: 0.7;
}

.metric-value {
  font-size: 11px;
  font-weight: 600;
}

@keyframes status-card-appear {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.floating-context-card {
  position: absolute;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  padding: 12px 16px;
  font-size: 12px;
  color: var(--foreground);
  animation: context-card-appear 0.8s ease-out both;
  max-width: 200px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.floating-context-card[data-delay="8s"] { animation-delay: 8s; }
.floating-context-card[data-delay="8.5s"] { animation-delay: 8.5s; }
.floating-context-card[data-delay="9s"] { animation-delay: 9s; }

.context-text {
  display: flex;
  align-items: center;
  font-weight: 500;
  line-height: 1.4;
}

@keyframes context-card-appear {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Mobile-First Responsive Design for Workflow */
@media (max-width: 768px) {
  /* Completely redesign the workflow section for mobile */
  .workflow-diagram > div:first-child {
    position: static !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
    min-height: auto !important;
  }
  
  /* Hide all absolute positioned elements and recreate with flexbox */
  .workflow-diagram .absolute {
    position: static !important;
    transform: none !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    width: 100% !important;
    height: auto !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    gap: 1rem !important;
  }
  
  /* Input Sources Section */
  .workflow-diagram .absolute:nth-child(2) {
    order: 1 !important;
  }
  
  .workflow-diagram .absolute:nth-child(2) .space-y-4 {
    display: flex !important;
    flex-direction: row !important;
    gap: 1rem !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
  }
  
  /* Processing Agents Section */
  .workflow-diagram .absolute:nth-child(3) {
    order: 2 !important;
  }
  
  .workflow-diagram .absolute:nth-child(3) .space-y-6 {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    width: 100% !important;
    max-width: 320px !important;
  }
  
  /* Tool Integrations Section */
  .workflow-diagram .absolute:nth-child(4) {
    order: 3 !important;
  }
  
  .workflow-diagram .absolute:nth-child(4) .space-y-4 {
    display: flex !important;
    flex-direction: row !important;
    gap: 1rem !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
  }
  
  /* Output Channels Section */
  .workflow-diagram .absolute:nth-child(5) {
    order: 4 !important;
  }
  
  .workflow-diagram .absolute:nth-child(5) .flex {
    display: flex !important;
    flex-direction: row !important;
    gap: 1rem !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
  }
  
  /* Status Card - Move to top */
  .workflow-diagram .absolute:nth-child(7) {
    order: 0 !important;
    align-self: flex-end !important;
    transform: scale(0.8) !important;
    margin-bottom: -1rem !important;
  }
  
  /* Hide complex elements that don't work well on mobile */
  .workflow-connections,
  .floating-context-card {
    display: none !important;
  }
  
  /* Mobile-optimized node sizes */
  .workflow-node {
    width: 70px !important;
    height: 70px !important;
    margin: 0 !important;
  }
  
  .node-icon {
    font-size: 20px !important;
    margin-bottom: 4px !important;
  }
  
  .node-label {
    font-size: 10px !important;
  }
  
  .workflow-agent-card {
    width: 100% !important;
    max-width: 300px !important;
    margin: 0 !important;
  }
  
  .integration-node {
    width: 60px !important;
    height: 60px !important;
    margin: 0 !important;
  }
  
  .integration-icon {
    font-size: 18px !important;
    margin-bottom: 4px !important;
  }
  
  .integration-label {
    font-size: 9px !important;
  }
  
  .channel-node {
    width: 75px !important;
    height: 75px !important;
    margin: 0 !important;
  }
  
  .channel-icon {
    font-size: 22px !important;
    margin-bottom: 6px !important;
  }
  
  .channel-label {
    font-size: 10px !important;
  }
  
  /* Agent card mobile optimizations */
  .agent-header {
    gap: 8px !important;
    margin-bottom: 8px !important;
  }
  
  .agent-avatar {
    width: 32px !important;
    height: 32px !important;
    font-size: 14px !important;
  }
  
  .agent-name {
    font-size: 13px !important;
  }
  
  .agent-model {
    font-size: 11px !important;
  }
  
  .task-text {
    font-size: 12px !important;
  }
  
  .agent-status {
    font-size: 11px !important;
  }
  
  /* Status card mobile optimizations */
  .workflow-status-card {
    min-width: 160px !important;
    padding: 12px !important;
  }
  
  .status-text {
    font-size: 11px !important;
  }
  
  .metric-label,
  .metric-value {
    font-size: 10px !important;
  }
}

@media (max-width: 480px) {
  /* Extra small screens */
  .workflow-node {
    width: 60px !important;
    height: 60px !important;
  }
  
  .node-icon {
    font-size: 18px !important;
  }
  
  .node-label {
    font-size: 9px !important;
  }
  
  .workflow-agent-card {
    padding: 12px !important;
    max-width: 280px !important;
  }
  
  .integration-node {
    width: 50px !important;
    height: 50px !important;
  }
  
  .integration-icon {
    font-size: 16px !important;
  }
  
  .integration-label {
    font-size: 8px !important;
  }
  
  .channel-node {
    width: 65px !important;
    height: 65px !important;
  }
  
  .channel-icon {
    font-size: 20px !important;
  }
  
  .channel-label {
    font-size: 9px !important;
  }
  
  .agent-avatar {
    width: 28px !important;
    height: 28px !important;
    font-size: 12px !important;
  }
  
  .agent-name {
    font-size: 12px !important;
  }
  
  .agent-model {
    font-size: 10px !important;
  }
  
  .task-text {
    font-size: 11px !important;
  }
  
  .workflow-status-card {
    transform: scale(0.75) !important;
  }
}

/* Enhanced Hero Section Styles */
.hero-background-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  opacity: 0.4;
  z-index: 1;
}

.hero-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 1px, transparent 1px);
  background-size: 80px 80px;
  animation: hero-grid-drift 30s linear infinite;
}

@keyframes hero-grid-drift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(80px, 80px); }
}

.floating-orb {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  backdrop-filter: blur(20px);
  animation: orb-float 8s ease-in-out infinite;
}

.floating-orb.orb-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  left: 20%;
  animation-delay: 0s;
}

.floating-orb.orb-2 {
  width: 80px;
  height: 80px;
  top: 70%;
  right: 15%;
  animation-delay: 2s;
}

.floating-orb.orb-3 {
  width: 60px;
  height: 60px;
  top: 40%;
  left: 80%;
  animation-delay: 4s;
}

.floating-orb.orb-4 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 10%;
  animation-delay: 6s;
}

@keyframes orb-float {
  0%, 100% { transform: translate3d(0, 0, 0) scale(1); }
  33% { transform: translate3d(20px, -30px, 0) scale(1.05); }
  66% { transform: translate3d(-15px, 20px, 0) scale(0.95); }
}

.minimal-agent-node {
  position: absolute;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  font-size: 18px;
  animation: minimal-node-pulse 4s ease-in-out infinite;
}

.node-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
  animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes minimal-node-pulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 1; }
}

@keyframes glow-pulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.5); opacity: 0.8; }
}

.flow-connection {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);
  animation: flow-line-pulse 3s ease-in-out infinite;
}

@keyframes flow-line-pulse {
  0%, 100% { opacity: 0.3; transform: scaleX(0.8); }
  50% { opacity: 1; transform: scaleX(1); }
}

.hero-title-section {
  text-align: center;
  margin-bottom: 4rem;
}

.hero-main-title {
  font-size: clamp(3rem, 8vw, 7rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  text-align: center;
}

.title-line-1 {
  display: block;
  color: white;
  margin-bottom: 0.5rem;
}

.title-line-2 {
  display: block;
  font-size: clamp(3.5rem, 9vw, 8rem);
  font-weight: 900;
  letter-spacing: -0.02em;
}

.gradient-text-enhanced {
  background: linear-gradient(135deg,
    #3b82f6 0%,
    #8b5cf6 25%,
    #06b6d4 50%,
    #8b5cf6 75%,
    #3b82f6 100%);
  background-size: 300% 300%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-flow-enhanced 6s ease infinite;
}

@keyframes gradient-flow-enhanced {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
  font-weight: 400;
}

.hero-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
}

.feature-pill-enhanced {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  z-index: 5;
}

.feature-pill-enhanced:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
}

.feature-pill-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.feature-pill-enhanced:hover::before {
  left: 100%;
}

.feature-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.25rem;
}

.feature-description {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.hero-chat-demo {
  display: flex;
  justify-content: center;
  margin-bottom: 4rem;
}

.chat-demo-container {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  width: 100%;
  max-width: 700px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.chat-demo-header {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-controls {
  display: flex;
  gap: 0.5rem;
}

.control-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  opacity: 0.8;
}

.control-dot.red { background: #ef4444; }
.control-dot.yellow { background: #f59e0b; }
.control-dot.green { background: #10b981; }

.chat-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.chat-title-text {
  font-size: 0.9rem;
  color: white;
  font-weight: 500;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(16, 185, 129, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 600;
}

.live-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #10b981;
  animation: live-pulse 2s ease-in-out infinite;
}

@keyframes live-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.chat-demo-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.demo-message {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  opacity: 0;
  animation: message-slide-in 0.8s ease-out forwards;
}

.demo-message.system {
  animation-delay: 0.5s;
}

.demo-message.agent:nth-child(2) {
  animation-delay: 1.5s;
}

.demo-message.agent:nth-child(3) {
  animation-delay: 2.5s;
}

@keyframes message-slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.system-avatar {
  background: linear-gradient(135deg, #374151, #4b5563);
  color: #9ca3af;
}

.agent-avatar {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.agent-avatar.claude {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.message-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.agent-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.agent-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
}

.agent-status {
  font-size: 0.75rem;
  color: #10b981;
  background: rgba(16, 185, 129, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  font-weight: 500;
}

.message-text {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  margin-bottom: 0.75rem;
}

.typing-effect {
  overflow: hidden;
  border-right: 2px solid #3b82f6;
  animation: typing 3s steps(40, end), blink-cursor 1s infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-cursor {
  from, to { border-color: transparent; }
  50% { border-color: #3b82f6; }
}

.message-progress {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-bar-demo {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
  width: 0%;
  animation: progress-fill 2s ease-out forwards;
}

@keyframes progress-fill {
  to { width: 75%; }
}

.channel-indicators {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.channel-indicator {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  animation: channel-ping 2s ease-in-out infinite;
}

.channel-indicator:nth-child(1) { animation-delay: 0.2s; }
.channel-indicator:nth-child(2) { animation-delay: 0.4s; }
.channel-indicator:nth-child(3) { animation-delay: 0.6s; }
.channel-indicator:nth-child(4) { animation-delay: 0.8s; }

@keyframes channel-ping {
  0%, 100% {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1);
  }
  50% {
    background: rgba(16, 185, 129, 0.3);
    transform: scale(1.1);
  }
}

.demo-typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  opacity: 0;
  animation: typing-indicator-appear 0.8s ease-out forwards;
}

.typing-dots-demo {
  display: flex;
  gap: 0.25rem;
}

.typing-dots-demo span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #3b82f6;
  animation: typing-bounce 1.4s ease-in-out infinite;
}

.typing-dots-demo span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots-demo span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing-indicator-appear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing-bounce {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-8px); }
}

.typing-text {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

.hero-cta-section {
  text-align: center;
  position: relative;
  z-index: 10;
  margin-top: 4rem;
}

.cta-buttons-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

@media (min-width: 640px) {
  .cta-buttons-wrapper {
    flex-direction: row;
  }
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.cta-button.primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.cta-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.cta-button:hover::before {
  left: 100%;
}

.hero-trust-indicators {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
  opacity: 0.8;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.trust-item i {
  color: #3b82f6;
  font-size: 1rem;
}

/* Responsive Design for Enhanced Hero */
@media (max-width: 768px) {
  .hero-features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .feature-pill-enhanced {
    padding: 1rem;
  }
  
  .hero-main-title {
    font-size: clamp(2rem, 6vw, 4rem);
  }
  
  .title-line-2 {
    font-size: clamp(2.5rem, 7vw, 5rem);
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .chat-demo-container {
    margin: 0 1rem;
  }
  
  .cta-buttons-wrapper {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-button {
    width: 100%;
    max-width: 300px;
  }
  
  .hero-trust-indicators {
    gap: 1rem;
  }
  
  .trust-item {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .floating-orb {
    display: none;
  }
  
  .minimal-agent-node {
    width: 30px;
    height: 30px;
    font-size: 14px;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .chat-demo-header {
    padding: 0.75rem 1rem;
  }
  
  .chat-demo-content {
    padding: 1rem;
  }
  
  .message-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .cta-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

/* Enhanced Agent Communication Styles */
.agent-communication-node {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  backdrop-filter: blur(15px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--accent-primary);
  animation: communication-node-pulse 4s ease-in-out infinite;
  transition: all 0.3s ease;
  z-index: 2;
}

.agent-communication-node:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.agent-communication-node.openai {
  background: linear-gradient(135deg, rgba(16, 163, 127, 0.2), rgba(16, 163, 127, 0.1));
  border-color: rgba(16, 163, 127, 0.4);
  color: #10a37f;
}

.agent-communication-node.anthropic {
  background: linear-gradient(135deg, rgba(196, 109, 57, 0.2), rgba(196, 109, 57, 0.1));
  border-color: rgba(196, 109, 57, 0.4);
  color: #c46d39;
}

.agent-communication-node.gemini {
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.2), rgba(66, 133, 244, 0.1));
  border-color: rgba(66, 133, 244, 0.4);
  color: #4285f4;
}

.agent-communication-node.groq {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(255, 107, 107, 0.1));
  border-color: rgba(255, 107, 107, 0.4);
  color: #ff6b6b;
}

.agent-communication-node.huggingface {
  background: linear-gradient(135deg, rgba(255, 206, 84, 0.2), rgba(255, 206, 84, 0.1));
  border-color: rgba(255, 206, 84, 0.4);
  color: #ffce54;
}

@keyframes communication-node-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

.bot-message-bubble {
  position: absolute;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  padding: 8px 12px;
  font-size: 11px;
  color: white;
  max-width: 180px;
  animation: message-bubble-appear 8s ease-in-out infinite;
  opacity: 0;
  transform-origin: bottom left;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 3;
}

.bot-message-bubble.openai {
  border-color: rgba(16, 163, 127, 0.4);
  background: linear-gradient(135deg, rgba(16, 163, 127, 0.15), rgba(16, 163, 127, 0.05));
}

.bot-message-bubble.anthropic {
  border-color: rgba(196, 109, 57, 0.4);
  background: linear-gradient(135deg, rgba(196, 109, 57, 0.15), rgba(196, 109, 57, 0.05));
}

.bot-message-bubble.gemini {
  border-color: rgba(66, 133, 244, 0.4);
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.15), rgba(66, 133, 244, 0.05));
}

.bot-message-bubble.groq {
  border-color: rgba(255, 107, 107, 0.4);
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(255, 107, 107, 0.05));
}

.bot-message-bubble.huggingface {
  border-color: rgba(255, 206, 84, 0.4);
  background: linear-gradient(135deg, rgba(255, 206, 84, 0.15), rgba(255, 206, 84, 0.05));
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.bot-name {
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.bot-message-bubble.openai .bot-name {
  color: #10a37f;
}

.bot-message-bubble.anthropic .bot-name {
  color: #c46d39;
}

.bot-message-bubble.gemini .bot-name {
  color: #4285f4;
}

.bot-message-bubble.groq .bot-name {
  color: #ff6b6b;
}

.bot-message-bubble.huggingface .bot-name {
  color: #ffce54;
}

.bot-message {
  font-size: 10px;
  line-height: 1.3;
  color: #e5e7eb;
  font-weight: 400;
}

.message-tail {
  position: absolute;
  bottom: -6px;
  left: 12px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid var(--glass-bg);
}

@keyframes message-bubble-appear {
  0%, 15% { 
    opacity: 0; 
    transform: scale(0.8) translateY(10px); 
  }
  20%, 80% { 
    opacity: 1; 
    transform: scale(1) translateY(0); 
  }
  85%, 100% { 
    opacity: 0; 
    transform: scale(0.8) translateY(-10px); 
  }
}

.communication-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);
  animation: communication-flow 6s ease-in-out infinite;
  opacity: 0;
  z-index: 1;
}

@keyframes communication-flow {
  0%, 20% { 
    opacity: 0; 
    transform: scaleX(0); 
  }
  25%, 75% { 
    opacity: 0.6; 
    transform: scaleX(1); 
  }
  80%, 100% { 
    opacity: 0; 
    transform: scaleX(0); 
  }
}

.data-particle {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--accent-primary);
  animation: particle-travel 4s ease-in-out infinite;
  opacity: 0;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
  z-index: 2;
}

@keyframes particle-travel {
  0% { 
    opacity: 0; 
    transform: translateX(0) translateY(0) scale(0.5); 
  }
  20% { 
    opacity: 1; 
    transform: translateX(50px) translateY(-20px) scale(1); 
  }
  50% { 
    opacity: 0.8; 
    transform: translateX(100px) translateY(10px) scale(0.8); 
  }
  80% { 
    opacity: 0.6; 
    transform: translateX(150px) translateY(-10px) scale(0.6); 
  }
  100% { 
    opacity: 0; 
    transform: translateX(200px) translateY(0) scale(0.3); 
  }
}

.agent-typing-indicator {
  position: absolute;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 6px 10px;
  backdrop-filter: blur(10px);
  animation: typing-indicator-pulse 6s ease-in-out infinite;
  opacity: 0;
  z-index: 3;
}

.agent-typing-indicator .typing-dots {
  display: flex;
  gap: 3px;
}

.agent-typing-indicator .typing-dots span {
  width: 4px;
  height: 4px;
  background: var(--accent-primary);
  border-radius: 50%;
  animation: typing-dot-bounce 1.4s ease-in-out infinite;
}

.agent-typing-indicator .typing-dots span:nth-child(2) { 
  animation-delay: 0.2s; 
}

.agent-typing-indicator .typing-dots span:nth-child(3) { 
  animation-delay: 0.4s; 
}

@keyframes typing-indicator-pulse {
  0%, 20% { 
    opacity: 0; 
    transform: scale(0.8); 
  }
  25%, 75% { 
    opacity: 1; 
    transform: scale(1); 
  }
  80%, 100% { 
    opacity: 0; 
    transform: scale(0.8); 
  }
}

@keyframes typing-dot-bounce {
  0%, 60%, 100% { 
    transform: translateY(0); 
  }
  30% { 
    transform: translateY(-6px); 
  }
}

/* Enhanced responsive design for communication elements */
@media (max-width: 768px) {
  .agent-communication-node {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .bot-message-bubble {
    max-width: 140px;
    font-size: 10px;
    padding: 6px 8px;
  }
  
  .bot-name {
    font-size: 8px;
  }
  
  .bot-message {
    font-size: 9px;
  }
  
  .communication-line {
    height: 1px;
  }
  
  .data-particle {
    width: 4px;
    height: 4px;
  }
}

@media (max-width: 480px) {
  .agent-communication-node {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
  
  .bot-message-bubble {
    max-width: 120px;
    font-size: 9px;
    padding: 5px 7px;
  }
  
  .bot-name {
    font-size: 7px;
  }
  
  .bot-message {
    font-size: 8px;
  }
  
  .agent-typing-indicator {
    padding: 4px 6px;
  }
  
  .agent-typing-indicator .typing-dots span {
    width: 3px;
    height: 3px;
  }
}

/* Enhanced CTA Section Styles */
.cta-main-container {
  position: relative;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 32px;
  padding: 4rem 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.cta-main-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.cta-title-section {
  text-align: center;
  margin-bottom: 3rem;
}

.cta-main-title {
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  text-align: center;
}

.cta-title-line-1 {
  display: block;
  color: white;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.cta-title-line-2 {
  display: block;
  font-size: clamp(3rem, 7vw, 5.5rem);
  font-weight: 900;
  letter-spacing: -0.02em;
  margin-bottom: 0.25rem;
}

.cta-title-accent {
  color: white;
  font-weight: 800;
}

.cta-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
  font-weight: 400;
}

.cta-features-showcase {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
  margin-bottom: 3rem;
}

.cta-feature-highlight {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.cta-feature-highlight:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
}

.cta-feature-highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.cta-feature-highlight:hover::before {
  left: 100%;
}

.feature-highlight-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.feature-highlight-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.highlight-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
}

.highlight-description {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
}

.cta-buttons-section {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
}

.cta-buttons-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.cta-button-enhanced {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 2rem;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-width: 280px;
  backdrop-filter: blur(20px);
}

.cta-button-enhanced.primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.cta-button-enhanced.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}

.cta-button-enhanced.secondary {
  background: rgba(255, 255, 255, 0.05);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.cta-button-enhanced.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-3px);
  box-shadow: 0 8px 32px rgba(255, 255, 255, 0.1);
}

.cta-button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.cta-button-enhanced:hover::before {
  left: 100%;
}

.button-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.cta-button-enhanced:hover .button-icon {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
}

.button-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
  text-align: left;
}

.button-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
}

.button-subtext {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.button-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.cta-button-enhanced:hover .button-arrow {
  transform: translateX(4px);
  color: white;
}

.cta-social-proof {
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
}

.social-proof-header {
  margin-bottom: 1.5rem;
}

.proof-text {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
  display: block;
  margin-bottom: 1rem;
}

.proof-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  display: block;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
}

.social-proof-badges {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.proof-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  transition: all 0.3s ease;
}

.proof-badge:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.proof-badge i {
  font-size: 0.9rem;
}

/* CTA Background Animation Elements */
.cta-agent-node {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: var(--accent-primary);
  animation: cta-node-pulse 6s ease-in-out infinite;
  z-index: 1;
}

.cta-agent-node.openai {
  background: linear-gradient(135deg, rgba(16, 163, 127, 0.15), rgba(16, 163, 127, 0.05));
  border-color: rgba(16, 163, 127, 0.3);
  color: #10a37f;
}

.cta-agent-node.anthropic {
  background: linear-gradient(135deg, rgba(196, 109, 57, 0.15), rgba(196, 109, 57, 0.05));
  border-color: rgba(196, 109, 57, 0.3);
  color: #c46d39;
}

.cta-agent-node.gemini {
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.15), rgba(66, 133, 244, 0.05));
  border-color: rgba(66, 133, 244, 0.3);
  color: #4285f4;
}

.cta-agent-node.groq {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(255, 107, 107, 0.05));
  border-color: rgba(255, 107, 107, 0.3);
  color: #ff6b6b;
}

@keyframes cta-node-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

.cta-flow-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
  animation: cta-flow-pulse 8s ease-in-out infinite;
  opacity: 0;
  z-index: 1;
}

@keyframes cta-flow-pulse {
  0%, 20% {
    opacity: 0;
    transform: scaleX(0);
  }
  25%, 75% {
    opacity: 0.6;
    transform: scaleX(1);
  }
  80%, 100% {
    opacity: 0;
    transform: scaleX(0);
  }
}

/* Responsive Design for Enhanced CTA */
@media (min-width: 640px) {
  .cta-buttons-enhanced {
    flex-direction: row;
    gap: 1.5rem;
  }
  
  .cta-features-showcase {
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  .cta-main-container {
    padding: 2rem 1.5rem;
    border-radius: 20px;
  }
  
  .cta-title-section {
    margin-bottom: 2rem;
  }
  
  .cta-main-title {
    font-size: clamp(2rem, 5vw, 3rem);
    margin-bottom: 1rem;
  }
  
  .cta-title-line-2 {
    font-size: clamp(2.5rem, 6vw, 3.5rem);
  }
  
  .cta-subtitle {
    font-size: 1.1rem;
  }
  
  .cta-features-showcase {
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
    margin-bottom: 2rem;
  }
  
  .cta-feature-highlight {
    width: 100%;
    max-width: 300px;
    padding: 0.75rem 1rem;
  }
  
  .cta-buttons-section {
    margin-bottom: 2rem;
  }
  
  .cta-buttons-enhanced {
    width: 100%;
  }
  
  .cta-button-enhanced {
    min-width: 100%;
    max-width: 320px;
  }
  
  .cta-social-proof {
    padding-top: 1.5rem;
  }
  
  .social-proof-header {
    margin-bottom: 1rem;
  }
  
  .proof-stats {
    gap: 1rem;
  }
  
  .stat-number {
    font-size: 1.25rem;
  }
  
  .social-proof-badges {
    gap: 1rem;
  }
  
  .proof-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
  
  .cta-agent-node {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .cta-main-container {
    padding: 2rem 1.5rem;
    border-radius: 20px;
  }
  
  .cta-title-section {
    margin-bottom: 2rem;
  }
  
  .cta-features-showcase {
    margin-bottom: 2rem;
  }
  
  .cta-feature-highlight {
    padding: 0.75rem 1rem;
  }
  
  .feature-highlight-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .highlight-title {
    font-size: 0.85rem;
  }
  
  .highlight-description {
    font-size: 0.7rem;
  }
  
  .cta-button-enhanced {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
  
  .button-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .button-text {
    font-size: 1rem;
  }
  
  .button-subtext {
    font-size: 0.8rem;
  }
  
  .proof-stats {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .stat-divider {
    display: none;
  }
  
  .social-proof-badges {
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
  }
  
  .proof-badge {
    min-width: 200px;
    justify-content: center;
  }
  
  .cta-agent-node {
    width: 30px;
    height: 30px;
    font-size: 12px;
  }
}
