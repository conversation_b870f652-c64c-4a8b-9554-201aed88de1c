export interface User {
  id: string
  email: string
  name: string
  image: string
}

export interface Tokens {
  access_token: string
  id_token?: string
  refresh_token?: string
  expires_in: number
  token_type: string
  expires_at: string
  jwt_access_token?: string
  provider?: string
}

export interface Session {
  user: User
  tokens?: Tokens | null
  expires: string
}

export type AuthStatus = 'loading' | 'authenticated' | 'unauthenticated'

export interface UseAuthReturn {
  session: Session | null
  status: AuthStatus
  signOut: () => void
  getAccessToken: () => string | null
  getGoogleAccessToken: () => string | null
  getIdToken: () => string | null
  getRefreshToken: () => string | null
  isTokenExpired: () => boolean
} 